<?php

namespace App\Jobs\MarketingCampaign;

use App\DTO\MarketingCampaign\MarketingSMS;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\Odin\StateAbbreviation;
use App\Exceptions\Text\SendTextFailedException;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\MarketingCampaign\Sending\MarketingCampaignSendingService;
use App\Services\PhoneNumber\PhoneNumberService;
use App\Services\QueueHelperService;
use App\Services\SMSOptOut\SmsOptOutService;
use App\Services\Text\TextService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendMarketingSMS implements ShouldQueue
{
    use Queueable;
    const int MAX_ATTEMPTS = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected MarketingSMS $sms,
        protected ?int $attempt = null,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(
        PhoneNumberService $phoneNumberService,
        TextService $textService,
        MarketingCampaignConsumerRepository $consumerRepository,
        MarketingCampaignSendingService $sendingService,
        SmsOptOutService $smsOptOutService,
    ): void
    {
        $recentlyContacted = $phoneNumberService->recentlyContacted(phone: $this->sms->getToPhone());

        $optedOut = $smsOptOutService->check($this->sms->getToPhone());

        $mc = $consumerRepository->get(id: $this->sms->getMarketingConsumerId());

        if ($recentlyContacted || $optedOut) {
            MarketingLogService::log(
                message: 'Failed to send SMS message',
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                context: [
                    'sms' => $this->sms->toArray(),
                    'error' => $recentlyContacted ? 'Phone recently contacted.' : 'Phone number opted out.',
                ],
                relations: [$mc]
            );

            $consumerRepository->updateStatus(
                mccIds: [$this->sms->getMarketingConsumerId()],
                status: $recentlyContacted ? MarketingCampaignConsumerStatus::UPLOADED : MarketingCampaignConsumerStatus::ERROR
            );

            return;
        }

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $mc->consumer->consumerProducts()->first();

        $stateAbbreviation = $consumerProduct->address->stateLocation?->state_abbr;

        $valid = app(PhoneNumberService::class)->validSendingTime(stateAbbreviation: $stateAbbreviation);

        if(!$valid) {
            $attemptCount = $this->attempt + 1;

            if($attemptCount > self::MAX_ATTEMPTS) {
                $logContext = [
                    'additional' => 'SMS attempted too many times'
                ];

                $consumerRepository->updateStatus(
                    mccIds: [$this->sms->getMarketingConsumerId()],
                    status: MarketingCampaignConsumerStatus::ERROR
                );
            } else {
                $nextSendTime = $sendingService->nextSendTime();

                $logContext = [
                    'next_scheduled_send_time_utc' => $nextSendTime->clone()->utc()->format('Y-m-d H:i:s'),
                    'next_scheduled_send_time' => $nextSendTime->clone()->format('Y-m-d H:i:s'),
                ];

                SendMarketingSMS::dispatch($this->sms, $attemptCount)->delay($nextSendTime);
            }

            MarketingLogService::log(
                message: 'Failed to send SMS message',
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                context: [
                    'sms' => $this->sms->toArray(),
                    'error' => 'Outside TCPA sending hours',
                    'consumer_state_abbr' => $stateAbbreviation ?: 'unknown',
                    'current_utc_time' => now()->format('Y-m-d H:i:s'),
                    'current_consumer_time' => $stateAbbreviation ? Carbon::now(StateAbbreviation::timeZone($stateAbbreviation))->format('Y-m-d H:i:s') : 'unknown',
                    'attempt_count' => $attemptCount,
                    ...$logContext
                ],
                relations: [$mc]
            );

            return;
        }

        try {
            $textService->send(sms: $this->sms, store: false);

            $phoneNumberService->markRecentlyContacted(phone: $this->sms->getToPhone());

            $consumerRepository->updateToSent(
                mccIds: [$this->sms->getMarketingConsumerId()]
            );
        } catch (SendTextFailedException $exception) {
            MarketingLogService::log(
                message: 'Failed to send SMS message',
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                stackTrace: $exception->getTraceAsString(),
                context: $exception->getSMS()->toArray(),
                relations: [$mc]
            );

            $consumerRepository->updateStatus(
                mccIds: [$this->sms->getMarketingConsumerId()],
                status: MarketingCampaignConsumerStatus::ERROR
            );
        }
    }
}
