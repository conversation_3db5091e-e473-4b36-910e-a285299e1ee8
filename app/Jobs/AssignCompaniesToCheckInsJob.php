<?php

namespace App\Jobs;

use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\Calendar\CheckIn;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * Attempts assigning a company_id to a demo
 * CheckIn must have company_id => null
 * Companies are linked via
 *  -> calendar_event_attendees->identified_contacts->possible_contacts->company_users->company_id
 * Companies will only be linked if there is exactly one company_id match
 */
class AssignCompaniesToCheckInsJob implements ShouldQueue
{
    use Queueable, Dispatchable, InteractsWithQueue;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->attemptAssigningMissingCompanyIds();

    }

    protected function attemptAssigningMissingCompanyIds(): void
    {
        $upsertData = DB::table(CheckIn::TABLE)
            ->select([
                CheckIn::TABLE .'.'. CheckIn::FIELD_ID,
                DB::raw(Company::TABLE .'.'. Company::FIELD_ID . ' AS company_id'),
                DB::raw('COUNT(DISTINCT ' . Company::TABLE . '.' . Company::FIELD_ID . ') AS company_count')
            ])->join(CalendarEvent::TABLE, CalendarEvent::TABLE .'.'. CalendarEvent::FIELD_ID, CheckIn::TABLE .'.'. CheckIn::FIELD_CALENDAR_EVENT_ID)
            ->join(CalendarEventAttendee::TABLE, CalendarEventAttendee::TABLE .'.'. CalendarEventAttendee::FIELD_CALENDAR_EVENT_ID, CalendarEvent::TABLE .'.'. CalendarEventAttendee::FIELD_ID)
            ->join(IdentifiedContact::TABLE, CalendarEventAttendee::TABLE .'.'. CalendarEventAttendee::FIELD_IDENTIFIED_CONTACT_ID, IdentifiedContact::TABLE .'.'. IdentifiedContact::FIELD_ID)
            ->join(PossibleContact::TABLE, fn(JoinClause $join) =>
            $join->on(IdentifiedContact::TABLE .'.'. IdentifiedContact::FIELD_NOMINATED_CONTACT_ID, '=', PossibleContact::TABLE .'.'. PossibleContact::FIELD_ID)
                ->orOn(IdentifiedContact::TABLE .'.'. IdentifiedContact::FIELD_ID, '=', PossibleContact::TABLE .'.'. PossibleContact::FIELD_IDENTIFIED_CONTACT_ID)
            )->join(CompanyUser::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID, '=', PossibleContact::TABLE .'.'. PossibleContact::FIELD_RELATION_ID)
                    ->where(PossibleContact::TABLE .'.'. PossibleContact::FIELD_RELATION_TYPE, 'App\\Models\\Odin\\CompanyUser')
                    ->where(CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID, '!=', 1)
                    ->where(CompanyUser::TABLE .'.'. CompanyUser::FIELD_EMAIL, 'not like', '%solarreviews.com')
                    ->where(CompanyUser::TABLE .'.'. CompanyUser::FIELD_EMAIL, 'not like', '%fixr.com')
            )->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID)
            ->whereNull(CheckIn::TABLE .'.'. CheckIn::FIELD_COMPANY_ID)
            ->groupBy(CheckIn::TABLE .'.'. CheckIn::FIELD_ID)
            ->having('company_count', '=', 1)
            ->get()
            ->reduce(function(array $output, $object) {
                $output[] = [
                    CheckIn::FIELD_ID         => $object->id,
                    CheckIn::FIELD_COMPANY_ID => $object->company_id,
                ];
                return $output;
            }, []);

        DB::beginTransaction();

        try {
            $rowsUpdated = DB::table(CheckIn::TABLE)
                ->upsert($upsertData, [CheckIn::FIELD_ID], [CheckIn::FIELD_COMPANY_ID]);
            DB::commit();
            $companiesUpdated = $rowsUpdated/2;
            logger()->info(__CLASS__ . " completed - identified $companiesUpdated companies.");

            return;
        }
        catch(Throwable $e) {
            logger()->error(__CLASS__ . " failed - " . $e->getMessage());
            DB::rollBack();
        }
    }
}
