<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\SalesOverviewRequest;
use App\Http\Resources\Calendar\CheckInResource;
use App\Http\Resources\Calendar\DemoResource;
use App\Http\Resources\Mailbox\MailboxEmailResource;
use App\Http\Resources\SalesOverview\CallResource;
use App\Http\Resources\SalesOverview\SalesOverviewResource;
use App\Http\Resources\SalesOverview\TextResource;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\CheckInService;
use App\Services\SalesOverviewService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class SalesOverviewController extends APIController
{
    const string STATUS   = 'status';
    const string DEMOS    = 'demos';
    const string PER_PAGE = 'per_page';
    const string PAGE     = 'page';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected SalesOverviewService $salesOverviewService,
        protected CheckInService $checkInService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param SalesOverviewRequest $request
     * @return array
     */
    public function getSalesOverview(SalesOverviewRequest $request): array
    {
        $filters = $request->validated();

        $data = $this->salesOverviewService->getSalesOverviewQuery(
            sortBy   : Arr::get($filters, 'sort_by', []),
            dateRange: Arr::get($filters, 'date_range', []),
            userId   : Arr::get($filters, 'user_id'),
        );

        return SalesOverviewResource::paginate($data);
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     *
     * @return AnonymousResourceCollection
     */
    public function getUserCalls(User $user, SalesOverviewRequest $request): AnonymousResourceCollection
    {
        return CallResource::collection(
            $this->salesOverviewService->getQueryForCalls(
                userId   : $user->id,
                dateRange: $request->validated('date_range') ?? [],
                direction: $request->validated('direction')
            )
                ->latest()
                ->paginate($this->request->get(self::PER_PAGE, 25))
        );
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     *
     * @return AnonymousResourceCollection
     */
    public function getUserTexts(User $user, SalesOverviewRequest $request): AnonymousResourceCollection
    {
        return TextResource::collection(
            $this->salesOverviewService->getQueryForTexts(
                userId   : $user->id,
                dateRange: $request->validated('date_range') ?? [],
                direction: $request->validated('direction')
            )
                ->latest()
                ->paginate($this->request->get(self::PER_PAGE, 25))
        );
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     *
     * @return AnonymousResourceCollection
     */
    public function getUserEmails(User $user, SalesOverviewRequest $request): AnonymousResourceCollection
    {
        return MailboxEmailResource::collection(
            $this->salesOverviewService->getQueryForEmails(
                userId   : $user->id,
                dateRange: $request->validated('date_range') ?? [],
                direction: $request->validated('direction')
            )
                ->latest()
                ->paginate($this->request->get(self::PER_PAGE, 25))
        );
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     * @return JsonResponse
     */
    public function getUserDemos(User $user, SalesOverviewRequest $request): JsonResponse
    {
        return $this->formatResponse([
            'demos' => DemoResource::paginate(
                $this->salesOverviewService->getDemos(
                    userId   : $user->id,
                    dateRange: $request->validated('date_range', []),
                )),
            'user'  => [
                'id'   => $user->{User::FIELD_ID},
                'name' => $user->{User::FIELD_NAME}
            ]
        ]);
    }


    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     * @return JsonResponse
     */
    public function getUserCheckIns(User $user, SalesOverviewRequest $request): JsonResponse
    {
        $checkIns = $this->checkInService->getFilteredCheckInQuery(
            userId: $user->id,
            filters: [
                'date_range' => $request->validated('date_range', []),
            ]
        );

        return $this->formatResponse([
            'check_ins' => CheckInResource::paginate($checkIns),
            'user'  => [
                'id'   => $user->{User::FIELD_ID},
                'name' => $user->{User::FIELD_NAME}
            ]
        ]);
    }


    /**
     * @param UserRepository $userRepository
     * @return JsonResponse
     */
    public function searchUsers(UserRepository $userRepository): JsonResponse
    {
        $searchText = $this->request->get('search', '');
        $excludeIds = $this->request->get('exclude_ids', []);
        $users = $userRepository->searchUsersForAutocomplete($searchText, [User::FIELD_ID, User::FIELD_NAME], $excludeIds);

        return $this->formatResponse([
            self::STATUS => true,
            User::TABLE  => $users->get()->toArray(),
        ]);
    }
}
