<?php

namespace App\Http\Controllers\MarketingCampaign;

use App\Exceptions\CustomValidationException;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\MarketingCampaign\SMS\ListMarketingSMSRequest;
use App\Http\Requests\MarketingCampaign\SMS\ListSMSOptOutsRequest;
use App\Http\Requests\MarketingCampaign\SMS\OptOutRequest;
use App\Http\Resources\MarketingCampaign\SMSOptOutResource;
use App\Http\Resources\MarketingCampaign\SMSResource;
use App\Models\SmsOptOut;
use App\Services\MarketingSMSService;
use App\Services\SMSOptOut\SmsOptOutService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MarketingSMSController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected MarketingSMSService $smsService,
        protected SmsOptOutService $smsOptOutService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListMarketingSMSRequest $request
     * @return array
     */
    public function listMarketingSMS(ListMarketingSMSRequest $request): array
    {
        $validated = $request->safe()->collect();

        $sms = $this->smsService->listMarketingSMS(
            fromPhone: $validated->get(ListMarketingSMSRequest::REQUEST_PHONE),
            message: $validated->get(ListMarketingSMSRequest::REQUEST_MESSAGE),
        );

        return SMSResource::paginate($sms);
    }

    /**
     * @param ListSMSOptOutsRequest $request
     * @return array
     */
    public function listSMSOptOut(ListSMSOptOutsRequest $request): array
    {
        $validated = $request->safe()->collect();

        $optOuts = $this->smsOptOutService->list(
            phone: $validated->get(ListSMSOptOutsRequest::REQUEST_PHONE),
        );

        return SMSOptOutResource::format($optOuts);
    }

    /**
     * @throws CustomValidationException
     */
    public function optOutPhone(OptOutRequest $request): JsonResponse
    {
        $validated = $request->safe()->collect();

        $optedOut = $this->smsOptOutService->add(
            phone: $validated->get(OptOutRequest::REQUEST_PHONE),
            reason: 'User Added'
        );

        return $this->formatResponse([
            'status' => !!$optedOut,
        ]);
    }

    /**
     * @param SmsOptOut $smsOptOut
     * @return JsonResponse
     */
    public function removeOptOut(SmsOptOut $smsOptOut): JsonResponse
    {
        $deleted = $this->smsOptOutService->delete(smsOptOut: $smsOptOut);

        return $this->formatResponse([
            'status' => !!$deleted,
        ]);
    }
}
