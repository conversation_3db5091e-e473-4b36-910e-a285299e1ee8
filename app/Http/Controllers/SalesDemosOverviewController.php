<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Calendar\DemoResource;
use App\Models\Calendar\Demo;
use App\Models\Conference\Conference;
use App\Models\Odin\Company;
use App\Services\DemoService;
use App\Services\Filterables\BaseFilterableService;
use App\Services\Filterables\SalesOverview\DemoFilterableService;
use App\Services\SalesOverviewService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SalesDemosOverviewController extends APIController
{
    const string STATUS   = 'status';
    const string DEMOS    = 'demos';
    const string PER_PAGE = 'per_page';
    const string PAGE     = 'page';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected SalesOverviewService $salesOverviewService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param DemoFilterableService $demoFilterableService
     * @return JsonResponse
     */
    public function getDemoFilters(DemoFilterableService $demoFilterableService): JsonResponse
    {
        $options = $demoFilterableService->getDisplayData();

        return $this->formatResponse([
            self::STATUS                              => !!$options,
            BaseFilterableService::KEY_FILTER_OPTIONS => $options,
        ]);
    }

    /**
     * @param DemoFilterableService $demoFilterableService
     * @param DemoService $demoService
     * @return JsonResponse
     */
    public function searchDemos(DemoFilterableService $demoFilterableService, DemoService $demoService): JsonResponse
    {
        $filterOptions = $this->request->get(BaseFilterableService::KEY_FILTERS, []);
        $userId        = $this->request->get(Demo::FIELD_USER_ID);
        $perPage       = $this->request->get(self::PER_PAGE, 25);
        $page          = $this->request->get(self::PAGE, 1);

        $demos = $demoService->getFilteredDemoQuery(
            userId: $userId,
            filters: $filterOptions,
            companyId: $this->request->get(Demo::FIELD_COMPANY_ID)
        )
           ->paginate(perPage: $perPage, page: $page);

        return $this->formatResponse([
            self::STATUS => !!$demos,
            self::DEMOS  => DemoResource::collection($demos)->resource,
        ]);
    }

    /**
     * @param DemoService $demoService
     *
     * @return StreamedResponse
     */
    public function exportDemos(DemoService $demoService): StreamedResponse
    {
        $CSVHeader = ['Host', 'Attendees','Has External Participants', 'Company', 'Link', 'Sales Development Representative', 'Status', 'Length', 'Start Time'];
        $handle = fopen('php://output', 'w');

        fputcsv($handle, $CSVHeader);

        $demoService->getFilteredDemoQuery(
            userId: $this->request->get(Demo::FIELD_USER_ID),
            filters: $this->request->get(BaseFilterableService::KEY_FILTERS, []),
            companyId: $this->request->get(Demo::FIELD_COMPANY_ID)
        )->chunk(100, function (Collection $demos) use ($handle) {
            /** @var Demo $demo */
            foreach ($demos as $demo) {
                fputcsv($handle, [
                    $demo->user?->name,
                    $demo->calendarEvent->attendees()->count(),
                    $demo->has_external_participants ? 'YES' : 'NO',
                    $demo->company?->name,
                    $demo->company ? config('app.url') . "/companies/{$demo->company->id}" : '',
                    $demo->company?->salesDevelopmentRepresentative?->name,
                    Str::headline($demo->status ?? 'unknown'),
                    round($demo->calendarEvent->conferences()->sum(Conference::FIELD_DURATION_IN_SECONDS) / 60) . ' minutes',
                    $demo->calendarEvent->start_time->timezone('America/Denver')->toDateTimeString() . ' (America/Denver)'
                ]);
            }
        });

        return response()->stream(function () use ($handle) {
            fclose($handle);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment;',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function associateCompanyWithDemo(): JsonResponse
    {
        $company = Company::query()->findOrFail($this->request->get('company_id'));
        $demo = Demo::query()->findOrFail($this->request->get('demo_id'));

        return $this->formatResponse([
            self::STATUS           => $demo->update([Demo::FIELD_COMPANY_ID => $company->id]),
            Demo::RELATION_COMPANY => [
                Company::FIELD_ID   => $company?->id,
                Company::FIELD_NAME => $company?->name,
            ],
        ]);
    }
}
