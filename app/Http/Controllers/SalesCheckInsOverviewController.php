<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Calendar\CheckInResource;
use App\Models\Calendar\CheckIn;
use App\Models\Odin\Company;
use App\Services\CheckInService;
use App\Services\Filterables\BaseFilterableService;
use App\Services\Filterables\SalesOverview\CheckInFilterableService;
use App\Services\SalesOverviewService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SalesCheckInsOverviewController extends APIController
{
    const string STATUS    = 'status';
    const string CHECK_INS = 'check_ins';
    const string PER_PAGE  = 'per_page';
    const string PAGE      = 'page';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected SalesOverviewService $salesOverviewService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CheckInFilterableService $checkInFilterableService
     * @return JsonResponse
     */
    public function getCheckInFilters(CheckInFilterableService $checkInFilterableService): JsonResponse
    {
        $options = $checkInFilterableService->getDisplayData();

        return $this->formatResponse([
            self::STATUS                              => !!$options,
            BaseFilterableService::KEY_FILTER_OPTIONS => $options,
        ]);
    }

    /**
     * @param CheckInFilterableService $demoFilterableService
     * @param CheckInService $checkInService
     * @return JsonResponse
     */
    public function searchCheckIns(CheckInFilterableService $demoFilterableService, CheckInService $checkInService): JsonResponse
    {
        $filterOptions = $this->request->get(BaseFilterableService::KEY_FILTERS, []);
        $userId = $this->request->get(CheckIn::FIELD_USER_ID);
        $perPage = $this->request->get(self::PER_PAGE, 25);
        $page = $this->request->get(self::PAGE, 1);

        $checkIns = $checkInService->getFilteredCheckInQuery(
            userId   : $userId,
            filters  : $filterOptions,
            companyId: $this->request->get(CheckIn::FIELD_COMPANY_ID)
        )
            ->paginate(perPage: $perPage, page: $page);

        return $this->formatResponse([
            self::STATUS    => !!$checkIns,
            self::CHECK_INS => CheckInResource::collection($checkIns)->resource,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function associateCompanyWithCheckIns(): JsonResponse
    {
        $company = Company::query()->findOrFail($this->request->get('company_id'));
        $demo = CheckIn::query()->findOrFail($this->request->get('demo_id'));

        return $this->formatResponse([
            self::STATUS              => $demo->update([CheckIn::FIELD_COMPANY_ID => $company->id]),
            CheckIn::RELATION_COMPANY => [
                Company::FIELD_ID   => $company?->id,
                Company::FIELD_NAME => $company?->name,
            ],
        ]);
    }
}
