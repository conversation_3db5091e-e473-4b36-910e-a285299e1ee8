<?php

namespace App\Http\Requests\Discovery;

use App\Rules\StrongPassword;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ForcedPasswordUpdateRequest extends FormRequest
{
    const string FIELD_TOKEN            = 'token';
    const string FIELD_EMAIL            = 'email';
    const string FIELD_NEW_PASSWORD     = 'password';
    const string FIELD_CONFIRM_PASSWORD = 'password_confirmation';


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_TOKEN            => ['required'],
            self::FIELD_EMAIL            => ['required', 'email'],
            self::FIELD_NEW_PASSWORD     => ['required', new StrongPassword],
            self::FIELD_CONFIRM_PASSWORD => ['required', 'same:' . self::FIELD_NEW_PASSWORD],
        ];
    }
}
