<?php

namespace App\Http\Resources;

use App\Models\Odin\ProductAssignment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ProductAssignment
 */
class ProductAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_name' => $this->company->name,
            'company_id' => $this->company->id,
            'campaign_id' => $this->budget?->budgetContainer->campaign?->id,
            'campaign_name' => $this->budget?->budgetContainer->campaign?->name,
            'chargeable' => $this->chargeable,
            'delivered' => $this->delivered,
            'cost' => $this->cost,
            'sale_type' => $this->saleType?->name,
            'product' => $this->consumerProduct->serviceProduct->product->name,
            'invoiced' => $this->invoiceItem()->exists(),
            'has_campaign_filter' => $this->budget->budgetContainer->companyCampaign?->filters()->exists() ?? false,
        ];
    }
}
