<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CheckIn;
use App\Models\Conference\Conference;
use Illuminate\Support\Str;

/**
 * @mixin CheckIn
 */
class CheckInResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'id'                   => $this->{CheckIn::FIELD_ID},
            'calendar_event_id'    => $this->{CheckIn::FIELD_CALENDAR_EVENT_ID},
            'calendar_event_title' => $this->calendarEvent->title,
            'company'              => [
                'name' => $this->company?->name ?? '-',
                'id'   => $this->company?->id,
            ],
            'status'               => Str::headline($this->demo_status ?? $this->status ?? 'unknown'),
            'event'                => new CalendarEventResource($this->calendarEvent),
            'user_name'            => $this->user_name,
            'user_id'              => $this->user_id,
            'count_participants'   => $this->calendarEvent->conferences->map(fn(Conference $conference) => $conference->participants()->count())->sum(),
            'has_external_participants' => $this->has_external_participants,

            'duration_in_call' => round($this->duration_in_call_in_seconds / 60) . ' minutes',
        ];
    }
}
