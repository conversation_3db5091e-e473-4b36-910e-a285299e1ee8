<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\SmsOptOut;
use Illuminate\Http\Request;

/**
 * @mixin SmsOptOut
 */
class SMSOptOutResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'phone' => $this->phone,
            'reason' => $this->reason,
            'created_at'   => CarbonHelper::parse($this->created_at)->toFormat(CarbonHelper::FORMAT_BASE_TIMEZONE, CarbonHelper::TIMEZONE_MOUNTAIN),
        ];
    }
}
