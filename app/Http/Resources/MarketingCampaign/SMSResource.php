<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Text;
use Illuminate\Http\Request;

/**
 * @mixin Text
 */
class SMSResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'from'         => $this->direction === Text::DIRECTION_INBOUND ? $this->other_number : $this->phone->phone,
            'message_body' => $this->message_body,
            'created_at'   => CarbonHelper::parse($this->created_at)->toFormat(CarbonHelper::FORMAT_BASE_TIMEZONE, CarbonHelper::TIMEZONE_MOUNTAIN),
            'direction'    => $this->direction,
        ];
    }
}
