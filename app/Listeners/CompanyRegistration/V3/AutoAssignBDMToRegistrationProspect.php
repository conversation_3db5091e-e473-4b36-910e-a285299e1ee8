<?php

namespace App\Listeners\CompanyRegistration\V3;

use App\Events\CompanyRegistration\V3\RegistrationStarted;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;

class AutoAssignBDMToRegistrationProspect implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(RegistrationStarted $event): void
    {
        $bdm = $this->determineBDMViaRoundRobin();

        if (filled($bdm)) {
            $event->newBuyerProspect->update([
                'user_id' => $bdm->id,
            ]);
        }
    }

    private function determineBDMViaRoundRobin()
    {
        $users = User::role('business-development-manager')->get();

        if ($users->isEmpty()) {
            return null;
        }

        $previousUserId = Cache::get('registration-bdm-round-robin');

        if (empty($previousUserId)) {
            return $users->first();
        }

        $previousUser = User::find($previousUserId);

        if (blank($previousUser)) {
            return $users->first();
        }

        $previousUserPosition = $users->search($previousUser);

        if ($previousUserPosition === false) {
            return $users->first();
        }

        return $users->get(($previousUserPosition + 1) % $users->count());
    }
}
