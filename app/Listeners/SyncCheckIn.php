<?php

namespace App\Listeners;

use App\Enums\AppFeature;
use App\Events\Calendar\CalendarEventSaved;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\User;
use App\Services\AppLogger;
use App\Services\CheckInService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SyncCheckIn implements ShouldQueue
{
    use InteractsWithQueue;

    public string $queue = 'mailbox_handle_mail_provider_event';


    public function __construct(
        protected CheckInService $checkInService
    )
    {

    }

    public function handle(CalendarEventSaved $event): void
    {
        $storedCalendarEvent = CalendarEvent::query()->findOrFail($event->calendarEventId);

        $logger = AppLogger::make(
            relations: [$storedCalendarEvent],
            feature  : AppFeature::CALENDAR,
            function : 'sync_demo'
        );

        $isCheckIn = $this->checkInService->checkIfEventIsCheckIn(
            calendarEvent: $storedCalendarEvent
        );

        if (!$isCheckIn) {
            return;
        }

        /** @var CalendarEventAttendee $host */
        $host = $storedCalendarEvent->attendees()
            ->where(CalendarEventAttendee::FIELD_IS_ORGANIZER, true)
            ->first();

        if (empty($host)) {
            $message = 'Organizer not found for demo';

            $logger->error(
                message: $message,
            );

            throw new Exception($message);
        }

        $userHost = User::query()
            ->where(User::FIELD_EMAIL, $host->{CalendarEventAttendee::FIELD_EMAIL})
            ->orWhereJsonContains(User::FIELD_EMAIL_ALIASES, $host->{CalendarEventAttendee::FIELD_EMAIL})
            ->first();

        if (empty($userHost)) {
            $message = 'Organizer not found for demo';

            $logger->error(
                message: $message
            );

            throw new Exception($message);
        }

        // Only create
        if ($storedCalendarEvent->{CalendarEvent::RELATION_USER}->{User::FIELD_ID} === $userHost->{User::FIELD_ID}) {
            $this->checkInService->updateOrCreate(
                calendarEventId: $storedCalendarEvent->{CalendarEvent::FIELD_ID},
                status         : $storedCalendarEvent->{CalendarEvent::FIELD_STATUS}->value,
                userId         : $userHost->{User::FIELD_ID},
            );
        }
    }
}
