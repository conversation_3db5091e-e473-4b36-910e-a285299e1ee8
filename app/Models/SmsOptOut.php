<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $id
 * @property int $phone
 * @property string $reason
 * @property Carbon $created_at
 */
class SmsOptOut extends BaseModel
{
    use HasFactory;

    const string TABLE = 'sms_opt_outs';

    const string FIELD_ID         = 'id';
    const string FIELD_PHONE      = 'phone';
    const string FIELD_REASON     = 'reason';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID,
    ];
}
