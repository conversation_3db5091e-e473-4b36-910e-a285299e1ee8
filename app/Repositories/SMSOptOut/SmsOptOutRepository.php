<?php

namespace App\Repositories\SMSOptOut;

use App\Models\SmsOptOut;
use Illuminate\Database\Eloquent\Builder;

class SmsOptOutRepository
{
    public function list(
        ?string $phone = null,
    ): Builder
    {
        return SmsOptOut::query()->when($phone, fn ($query) => $query->where(SmsOptOut::FIELD_PHONE,'like', "%$phone%"));
    }

    public function add(
        string $phone,
        string $reason,
    ): SmsOptOut
    {
        return SmsOptOut::query()->create([
            SmsOptOut::FIELD_PHONE => $phone,
            SmsOptOut::FIELD_REASON => $reason,
        ]);
    }

    public function delete(SmsOptOut $smsOptOut): ?bool
    {
        return $smsOptOut->delete();
    }

    public function exists(string $phone): bool
    {
        return SmsOptOut::query()->where(SmsOptOut::FIELD_PHONE, $phone)->exists();
    }

}
