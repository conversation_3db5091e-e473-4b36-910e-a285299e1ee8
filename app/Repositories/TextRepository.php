<?php

namespace App\Repositories;

use App\Enums\CommunicationRelationTypes;
use App\Models\BaseModel;
use App\Models\Text;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class TextRepository
{
    /**
     * @param array|null $otherNumbers
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param string|null $direction
     * @param int|null $phoneId
     * @param array<BaseModel>|null $relations
     * @param string|null $externalReference
     * @param string|null $externalType
     * @param string|null $otherNumber
     * @param string|null $message
     * @param string|null $orderBy
     * @param string|null $orderDirection
     * @param CommunicationRelationTypes|null $relationType
     * @return Builder
     */
    public function list(
        ?array  $otherNumbers = [],
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        ?string $direction = null,
        ?int    $phoneId = null,
        ?array  $relations = [],
        ?string $externalReference = null,
        ?string $externalType = null,
        ?string $otherNumber = null,
        ?string $message = null,
        ?string $orderBy = null,
        ?string $orderDirection = null,
        ?CommunicationRelationTypes $relationType = null,
    ): Builder
    {
        return Text::query()
            ->when($phoneId, fn($query) => $query->where(Text::FIELD_PHONE_ID, $phoneId))
            ->when($direction, fn($query) => $query->where(Text::FIELD_DIRECTION, $direction))
            ->when($startDate, fn($query) => $query->where(Text::FIELD_CREATED_AT, '>=', $startDate))
            ->when($endDate, fn($query) => $query->where(Text::FIELD_CREATED_AT, '<=', $endDate))
            ->when(filled($otherNumbers), fn($query) => $query->whereIn(Text::FIELD_OTHER_NUMBER, $otherNumbers))
            ->when($otherNumber, fn($query) => $query->where(Text::FIELD_OTHER_NUMBER,'like', "%$otherNumber%"))
            ->when($message, fn($query) => $query->where(Text::FIELD_MESSAGE_BODY, 'like', "%$message%"))
            ->when($externalReference, fn($query) => $query->where(Text::FIELD_EXTERNAL_REFERENCE, $externalReference))
            ->when($externalType, fn($query) => $query->where(Text::FIELD_EXTERNAL_TYPE, $externalType))
            ->when($relationType, fn($query) => $query->where(Text::FIELD_RELATION_TYPE, $relationType->value))
            ->when(filled($relations), function ($query) use ($relations) {
                $query->where(function (Builder $query) use ($relations) {
                    foreach ($relations as $relation) {
                        $query->orWhere(function (Builder $query) use ($relation) {
                            $query->where(Text::FIELD_RELATION_TYPE, CommunicationRelationTypes::fromClass($relation::class)->value)
                                ->where(Text::FIELD_RELATION_ID, $relation->id);
                        });
                    }
                });
            })
            ->when($orderBy, fn($query) => $query->orderBy($orderBy, $orderDirection));
    }

}
