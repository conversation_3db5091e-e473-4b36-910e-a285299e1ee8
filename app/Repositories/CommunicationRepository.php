<?php

namespace App\Repositories;

use App\Builders\Odin\Communication\CommunicationLogsBuilder;
use App\Enums\CommunicationRelationTypes;
use App\Models\Call;
use App\Models\CallRecording;
use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessingTimeframe;
use App\Models\LeadProcessor;
use App\Models\Odin\Consumer;
use App\Models\Phone;
use App\Models\Text;
use App\Models\TextMediaAsset;
use App\Models\TimeframeContactBuffers;
use App\Models\User;
use App\Models\Voicemail;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CommunicationRepository
{
    const int TWENTY_FOUR_HOURS = 60 * 60 * 24;
    const int FORTY_EIGHT_HOURS = 60 * 60 * 24 * 2;
    const int SEVEN_DAYS        = 60 * 60 * 24 * 7;
    const int THIRTY_DAYS       = 60 * 60 * 24 * 30;

    /**
     * @param string $serviceName
     * @param string $reference
     * @param int $fromPhoneId
     * @param string $toNumber
     * @param string $body
     * @param string|null $relType
     * @param int|null $relId
     * @param int|null $companyCadenceActionId
     * @return Text
     */
    public function updateOrCreateOutboundSMS(
        string  $serviceName,
        string  $reference,
        int     $fromPhoneId,
        string  $toNumber,
        string  $body,
        ?string $relType,
        ?int    $relId,
        ?int    $companyCadenceActionId = null
    ): Text
    {
        /** @var Text $text */
        $text = Text::query()->updateOrCreate([
            Text::FIELD_EXTERNAL_TYPE      => $serviceName,
            Text::FIELD_EXTERNAL_REFERENCE => $reference
        ], [
            Text::FIELD_PHONE_ID                        => $fromPhoneId,
            Text::FIELD_OTHER_NUMBER                    => $toNumber,
            Text::FIELD_MESSAGE_BODY                    => $body,
            Text::FIELD_DIRECTION                       => Text::DIRECTION_OUTBOUND,
            Model::CREATED_AT                           => Carbon::now('UTC'),
            Model::UPDATED_AT                           => Carbon::now('UTC'),
            Text::FIELD_RELATION_ID                     => $relId,
            Text::FIELD_RELATION_TYPE                   => $relType,
            Text::FILED_COMPANY_CADENCE_GROUP_ACTION_ID => $companyCadenceActionId
        ]);

        return $text;
    }

    public function updateOrCreateInboundSMS(
        string  $serviceName,
        string  $reference,
        string  $fromNumber,
        int     $toPhoneId,
        string  $body,
        ?string $relType = null,
        ?int    $relId = null,
    ): Text
    {
        /** @var Text $text */
        $text = Text::query()->updateOrCreate([
            Text::FIELD_EXTERNAL_TYPE      => $serviceName,
            Text::FIELD_EXTERNAL_REFERENCE => $reference
        ], [
            Text::FIELD_PHONE_ID      => $toPhoneId,
            Text::FIELD_OTHER_NUMBER  => $fromNumber,
            Text::FIELD_MESSAGE_BODY  => $body,
            Text::FIELD_DIRECTION     => Text::DIRECTION_INBOUND,
            Model::CREATED_AT         => Carbon::now('UTC'),
            Model::UPDATED_AT         => Carbon::now('UTC'),
            Text::FIELD_RELATION_ID   => $relId,
            Text::FIELD_RELATION_TYPE => $relType,
        ]);

        return $text;
    }

    /**
     * @param string $number
     * @return Phone|null
     */
    public function getPhoneFromPhoneNumber(string $number): ?Phone
    {
        /** @var Phone $phone */
        $phone = Phone::query()
            ->where(Phone::FIELD_PHONE, $this->formatPhoneNumber($number))
            ->first();

        return $phone;
    }

    /**
     * @param string $serviceName
     * @param string $userPhone
     * @param string $contactPhone
     * @param string $externalReference
     * @param string $callResult
     * @param string|null $relationType
     * @param int|null $relationId
     * @return Call
     */
    public function updateOrCreateOutboundCall(
        string  $serviceName,
        string  $userPhone,
        string  $contactPhone,
        string  $externalReference,
        string  $callResult,
        ?string $relationType,
        ?int    $relationId
    ): Call
    {
        /** @var Phone $phone */
        $phone = Phone::query()->where(Phone::FIELD_PHONE, $this->formatPhoneNumber($userPhone))->first();
        /** @var Call $call */
        $call = Call::firstOrCreate(
            [
                Call::FIELD_EXTERNAL_TYPE      => $serviceName,
                Call::FIELD_EXTERNAL_REFERENCE => $externalReference,
                Call::FIELD_OTHER_NUMBER       => $this->formatPhoneNumber($contactPhone),
                Call::FIELD_PHONE_ID           => $phone->id,
                Call::FIELD_DIRECTION          => Call::DIRECTION_OUTBOUND
            ],
            [
                Call::FIELD_RESULT => $callResult,
            ]
        );
        if (!$call->call_start) {
            $call->call_start = Carbon::now();
        }
        if ($callResult !== Call::RESULT_INITIAL) {
            $call->call_end = Carbon::now();
        }
        if ($relationType && $relationId) {
            $call->{Call::FIELD_RELATION_TYPE} = $relationType;
            $call->{Call::FIELD_RELATION_ID} = $relationId;
        }

        $call->result = $callResult;

        $call->save();

        return $call;
    }

    /**
     * @param string $serviceName
     * @param string $userPhone
     * @param string $contactPhone
     * @param string $externalReference
     * @param string $callResult
     * @param string|null $relationType
     * @param int|null $relationId
     * @return int
     */
    public function updateOrCreateInboundCall(
        string  $serviceName,
        string  $userPhone,
        string  $contactPhone,
        string  $externalReference,
        string  $callResult,
        ?string $relationType,
        ?int    $relationId
    ): int
    {
        /** @var Phone $phone */
        $phone = $this->getPhoneFromPhoneNumber($userPhone);
        /** @var Call $call */
        $call = Call::firstOrCreate(
            [
                Call::FIELD_EXTERNAL_TYPE      => $serviceName,
                Call::FIELD_EXTERNAL_REFERENCE => $externalReference,
                Call::FIELD_OTHER_NUMBER       => $this->formatPhoneNumber($contactPhone),
                Call::FIELD_PHONE_ID           => $phone->id,
                Call::FIELD_DIRECTION          => Call::DIRECTION_INBOUND
            ],
            [
                Call::FIELD_RESULT => $callResult
            ]
        );
        if (!$call->call_start) {
            $call->call_start = Carbon::now();
        }
        if ($callResult !== Call::RESULT_INITIAL) {
            $call->call_end = Carbon::now();
        }
        if ($relationType && $relationId) {
            $call->relation_type = $relationType;
            $call->relation_id = $relationId;
        }
        $call->result = $callResult;
        $call->save();

        return $call->{Call::FIELD_ID};
    }

    /**
     * @param string $phoneNumber
     * @return string
     */
    private function formatPhoneNumber(string $phoneNumber): string
    {
        $phoneNumber = Str::replace("+1", "", $phoneNumber ?? "");
        return Str::replace("-", "", $phoneNumber ?? "");
    }

    /**
     * Returns a user by their phone number.
     *
     * @param string $number
     * @return User|null
     */
    public function getUserByNumber(string $number): ?User
    {
        /** @var User $user */
        $user = User::query()
            ->whereHas(User::RELATION_PHONES, fn($query) => $query->where(Phone::FIELD_PHONE, $number))
            ->first();

        return $user;
    }

    /**
     * Creates a voicemail.
     *
     * @param string $from
     * @param int $userId
     * @param string $callSid
     * @param string $voicemailLink
     * @param string|null $fromType
     * @param int|null $fromId
     * @param float $duration
     * @return Voicemail
     */
    public function saveVoicemail(
        string  $from,
        int     $userId,
        string  $callSid,
        string  $voicemailLink,
        ?string $fromName,
        ?string $fromType,
        ?int    $fromId,
        float   $duration
    ): Voicemail
    {
        $voicemail = new Voicemail();
        $voicemail->user_id = $userId;
        $voicemail->from_number = $from;
        $voicemail->from_name = $fromName;
        $voicemail->from_type = $fromType;
        $voicemail->from_id = $fromId;
        $voicemail->voicemail_link = $voicemailLink;
        $voicemail->call_sid = $callSid;
        $voicemail->duration = $duration;
        $voicemail->read = false;
        $voicemail->save();

        return $voicemail;
    }

    public function getSMSHistoryForNumber(string $number): Collection
    {
        return Text::query()->with(Text::RELATION_TEXT_MEDIA_ASSET)->where(Text::FIELD_OTHER_NUMBER, $number)->oldest()->get();
    }

    public function getLastContactedDateForNumber(string $phoneNumber)
    {
        $lastContactedText = Text::query()->where(Text::FIELD_OTHER_NUMBER, $phoneNumber)->latest()->get()->first();
        return $lastContactedText['updated_at']->timestamp;
    }

    /**
     * @param int $userId
     * @return Collection<int, Voicemail>
     */
    public function getVoicemailsByUserId(int $userId): Collection
    {
        return Voicemail::query()->where(Voicemail::FIELD_USER_ID, $userId)->latest()->get();
    }

    /**
     * @param int $voicemailId
     * @return bool
     */
    public function markVoicemailRead(int $voicemailId): bool
    {
        $voicemail = Voicemail::findOrFail($voicemailId);
        if (!$voicemail->read) {
            $voicemail->read = true;
            return $voicemail->save();
        } else {
            return false;
        }
    }

    /**
     * @param string $callSid
     * @return null|Call
     */
    public function getCallByExternalReference(string $callSid): ?Call
    {
        /** @var Call $call */
        $call = Call::query()->where(Call::FIELD_EXTERNAL_REFERENCE, $callSid)->first();

        return $call;
    }

    /**
     * @param int $callId
     * @param string $reference
     * @param int $recordingDuration
     * @param string $recordingUrl
     * @return CallRecording
     */
    public function saveCallRecording(
        int    $callId,
        string $reference,
        int    $recordingDuration,
        string $recordingUrl
    ): CallRecording
    {
        return CallRecording::firstOrCreate([
            CallRecording::FIELD_CALL_ID => $callId
        ], [
            CallRecording::FIELD_DURATION_SECONDS => $recordingDuration,
            CallRecording::FIELD_RECORDING_LINK   => $recordingUrl,
            CallRecording::FIELD_SID              => $reference
        ]);
    }

    /**
     * @param int $leadId
     * @return Collection<int, CallRecording>
     */
    public function getCallRecordingsByLeadId(int $leadId): Collection
    {
        return CallRecording::query()
            ->leftJoin(Call::TABLE, CallRecording::TABLE . '.' . CallRecording::FIELD_CALL_ID, '=', Call::TABLE . '.' . Call::FIELD_ID)
            ->whereIn(Call::FIELD_RELATION_TYPE, [CommunicationRelationTypes::LEAD->value, CommunicationRelationTypes::CONSUMER_PRODUCT->value])
            ->where(Call::TABLE . '.' . Call::FIELD_RELATION_ID, $leadId)
            ->get();
    }


    /**
     * Get communication logs under a list of phones
     *
     * @param int[] $allUserPhonesIds
     * @param array $filters
     * @return LengthAwarePaginator
     * @throws \Exception
     */
    public function getLogsByPhones(array $allUserPhonesIds, array $filters): LengthAwarePaginator
    {
        $query = CommunicationLogsBuilder::query()->setUserPhonesIds($allUserPhonesIds);

        if (isset($filters['user_phone']))
            $query->setUserPhone($filters['user_phone']);

        if (isset($filters['external_phone']))
            $query->setExternalPhone($filters['external_phone']);

        if (isset($filters['communication_description']))
            $query->setCommunicationDescription($filters['communication_description']);

        if (isset($filters['communication_type'])) {
            $query->setCommunicationTypes($filters['communication_type']);
        }

        if (isset($filters['date_time']['from'])) {
            $query->setDateFrom($filters['date_time']['from']);
        }

        if (isset($filters['date_time']['to'])) {
            $query->setDateTo($filters['date_time']['to']);
        }

        return $query->paginate($filters['page'] ?? 1, $filters['perPage'] ?? 100);
    }


    /**
     * Get log data to identify the caller
     *
     * @param int $id
     * @param string $type
     * @return array|void
     */
    public function getLogFurtherData(int $id, string $type)
    {
        if ($type === 'Call') {
            // Return call recording
            return [
                'call_recording' => CallRecording::query()->where(CallRecording::FIELD_CALL_ID, $id)->first()
            ];
        }

        if ($type === 'Text') {
            // Return all messages from that specific chat
            $text = Text::query()->where(Text::FIELD_ID, $id)->first();

            return [
                'messages' => Text::query()
                    ->with(Text::RELATION_TEXT_MEDIA_ASSET)
                    ->where(Text::FIELD_PHONE_ID, $text->{Text::FIELD_PHONE_ID})
                    ->where(Text::FIELD_OTHER_NUMBER, $text->{Text::FIELD_OTHER_NUMBER})
                    ->orderBy(Text::FIELD_CREATED_AT)
                    ->get()
            ];
        }
    }

    /**
     * Count interactions by relation type, id and number id
     * @param string $relationType
     * @param string $relationId
     * @param int $userPhoneId
     * @return int
     */
    public function countInteractions(string $relationType, string $relationId, int $userPhoneId): int
    {
        $callInteractions = Call::query()
            ->where(Call::FIELD_RELATION_TYPE, $relationType)
            ->where(Call::FIELD_RELATION_ID, $relationId)
            ->where(Call::FIELD_PHONE_ID, $userPhoneId)
            ->count();

        $textInteractions = Text::query()
            ->where(Text::FIELD_RELATION_TYPE, $relationType)
            ->where(Text::FIELD_RELATION_ID, $relationId)
            ->where(Text::FIELD_PHONE_ID, $userPhoneId)
            ->count();

        return $callInteractions + $textInteractions;
    }

    /**
     * @param Text $text
     * @param array $mediaArray
     * @return void
     */
    public function createMediaForText(Text $text, array $mediaArray): void
    {
        foreach ($mediaArray as $media) {
            $textMediaAsset = new TextMediaAsset();
            $textMediaAsset->text_id = $text->id;
            $textMediaAsset->url = $media[TextMediaAsset::FIELD_URL];
            $textMediaAsset->type = $media[TextMediaAsset::FIELD_TYPE];
            $textMediaAsset->save();
        }
    }

    /**
     * @param int|null $consumerProductId
     * @return void
     */
    public function updateMostRecentCommunication(int $consumerProductId = null): void
    {
        LeadProcessingCommunication::query()
            ->where(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)
            ->update([LeadProcessingCommunication::FIELD_MOST_RECENT => false]);

        LeadProcessingCommunication::query()
            ->where(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->orderBy(LeadProcessingCommunication::FIELD_ID, 'desc')
            ->limit(1)
            ->update([LeadProcessingCommunication::FIELD_MOST_RECENT => true]);
    }

    /**
     * @param Consumer $consumer
     * @return bool
     */
    public function canCallConsumer(Consumer $consumer): bool
    {
        $productProcessingRepository = app(ProductProcessingRepository::class);

        $leadProcessorId = $productProcessingRepository->getLeadProcessorByUserId(Auth::user()->id)?->{LeadProcessor::FIELD_ID} ?? 0;

        $mostRecentCommunication = $consumer
            ->{Consumer::RELATION_LEAD_PROCESSING_COMMUNICATIONS}
            ?->where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)
            ?->first();

        if (!empty($leadProcessorId)
            && !empty($mostRecentCommunication)) {
            if ($mostRecentCommunication->{LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID} === $leadProcessorId) {
                return true;
            }

            return $mostRecentCommunication->{LeadProcessingCommunication::CREATED_AT}->diffInHours(Carbon::now('UTC')) >= 8;
        } else {
            $lastContacted = $mostRecentCommunication
                ?->{LeadProcessingCommunication::CREATED_AT}
                ?->timestamp;

            if (empty($lastContacted)) {
                return true;
            }

            $timeframeBuffers = LeadProcessingTimeframe::query()
                ->join(TimeframeContactBuffers::TABLE, function ($join) {
                    $join->on(
                        TimeframeContactBuffers::TABLE . '.' . TimeframeContactBuffers::FIELD_TIMEFRAME_ID,
                        '=',
                        LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID
                    );
                })
                ->selectRaw(implode(',', [
                    LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_KEY . " AS `key`",
                    TimeframeContactBuffers::TABLE . '.' . TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS . " * 60 * 60 AS `buffer_secs`"
                ]))
                ->pluck('buffer_secs', 'key')
                ->toArray();

            $now = CarbonImmutable::now('UTC')->timestamp;

            $timeSinceLastContact = $now - $lastContacted;

            $consumerAge = $now - $consumer->{Consumer::CREATED_AT}->timestamp;

            if ($consumerAge <= self::TWENTY_FOUR_HOURS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS;
            } else if ($consumerAge >= (self::TWENTY_FOUR_HOURS + 1)
                && $consumerAge <= self::FORTY_EIGHT_HOURS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_TWENTY_FOUR_TO_FORTY_EIGHT_HRS;
            } else if ($consumerAge >= (self::FORTY_EIGHT_HOURS + 1)
                && $consumerAge <= self::SEVEN_DAYS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_TWO_TO_SEVEN_DAYS;
            } else if ($consumerAge >= (self::SEVEN_DAYS + 1)
                && $consumerAge <= self::THIRTY_DAYS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_SEVEN_TO_THIRTY_DAYS;
            } else {
                $timeframeKey = LeadProcessingTimeframe::KEY_THIRTY_TO_NINETY_DAYS;
            }

            return $timeSinceLastContact >= (int)$timeframeBuffers[$timeframeKey];
        }
    }
}
