<?php

namespace App\Repositories\CheckIn;

use App\Enums\Calendar\CheckInStatus;
use App\Models\Calendar\CheckIn;

class CheckInRepository
{
    /**
     * @param int $calendarEventId
     * @param string $status
     * @param int $userId
     * @return CheckIn
     */
    public function updateOrCreate(
        int $calendarEventId,
        string $status,
        int $userId,
    ): CheckIn
    {
        $existing = CheckIn::query()->where([
            CheckIn::FIELD_CALENDAR_EVENT_ID => $calendarEventId,
        ])->first();

        // Keep the status as completed
        if ($existing?->status === CheckInStatus::COMPLETED->value) {
            $status = CheckInStatus::COMPLETED->value;
        }

        return CheckIn::query()->updateOrCreate([
            CheckIn::FIELD_CALENDAR_EVENT_ID => $calendarEventId,
        ], [
            CheckIn::FIELD_STATUS  => $status,
            CheckIn::FIELD_USER_ID => $userId,
        ]);
    }
}
