<?php

namespace App\Enums;

use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;

enum CommunicationRelationTypes: string
{
    case LEAD                = 'lead';
    case COMPANY_USER        = 'company_user';
    case COMPANY_LOCATIONS   = 'company_locations';
    case COMPANY_LOCATION    = 'company_location';
    case COMPANY             = 'company';
    case LEGACY_COMPANY      = 'legacy-company';
    case CONSUMER_PRODUCT    = 'consumer_product';
    case CONSUMERS           = 'consumers';
    case MARKETING_CONSUMERS = 'marketing_consumers';

    public static function fromClass(string $modelClass): self
    {
        return match ($modelClass) {
            CompanyLocation::class           => self::COMPANY_LOCATIONS,
            CompanyUser::class               => self::COMPANY_USER,
            Company::class                   => self::COMPANY,
            MarketingCampaignConsumer::class => self::MARKETING_CONSUMERS,
        };
    }

}
