<?php

namespace App\Enums\MarketingCampaigns;

use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\MarketingLogRelation;
use App\Models\Text;

enum MarketingLogRelationType: string
{
    case MARKETING_CAMPAIGN = 'marketing_campaign';
    case MARKETING_CAMPAIGN_CONSUMER = 'marketing_campaign_consumer';
    case TEXT = 'text';

    /**
     * @param string $class
     * @return self
     */
    public static function fromClass(string $class): self
    {
        return match ($class) {
            MarketingCampaign::class => self::MARKETING_CAMPAIGN,
            MarketingCampaignConsumer::class => self::MARKETING_CAMPAIGN_CONSUMER,
            Text::class => self::TEXT,
        };
    }

    /**
     * @return string
     */
    public function toClass(): string
    {
        return match ($this) {
            self::MARKETING_CAMPAIGN => MarketingCampaign::class,
            self::MARKETING_CAMPAIGN_CONSUMER => MarketingCampaignConsumer::class,
            self::TEXT => Text::class,
        };
    }

    /**
     * @param MarketingLogRelation $relation
     * @return string
     */
    public function getRelationName(MarketingLogRelation $relation): string
    {
        return match ($this) {
            self::MARKETING_CAMPAIGN => $relation->{MarketingLogRelation::RELATION_RELATION}->{MarketingCampaign::FIELD_NAME},
            self::MARKETING_CAMPAIGN_CONSUMER => $relation->{MarketingLogRelation::RELATION_RELATION}->{MarketingCampaignConsumer::RELATION_CONSUMER}->getFullName(),
            self::TEXT => 'Text Message'
        };
    }

    /**
     * @return string
     */
    public function getShortType(): string
    {
        return match ($this) {
            self::MARKETING_CAMPAIGN_CONSUMER => 'MCC',
            self::MARKETING_CAMPAIGN => 'MC',
            self::TEXT => 'SMS',
        };
    }

}
