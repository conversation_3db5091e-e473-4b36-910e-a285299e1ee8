<?php

namespace App\Services\PhoneNumber;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Odin\Consumer;
use App\Services\ContactCacheService;
use App\Services\HelperService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;

class PhoneNumberService
{
    public function __construct(
        protected ContactCacheService $contactCacheService,
    )
    {
    }

    public function recentlyContacted(
        string $phone
    ): bool
    {
        $formattedPhone = $this->formatPhoneNumber(phone: $phone);

        return $this->contactCacheService->recentlyContacted(
            contactVector: $formattedPhone
        );
    }

    public function markRecentlyContacted(
        string $phone
    ): void
    {
        $phone = $this->formatPhoneNumber(phone: $phone);

        $this->contactCacheService->markRecentlyContacted(
            contactVector: $phone,
        );
    }

    public function formatPhoneNumber(
        string $phone,
    ): ?string
    {
        return $phone; //todo
    }

    /**
     * converts phone number to E.164 standard
     *
     * @param string $phone
     * @return string|null
     */
    public function normalisePhoneNumber(string $phone): ?string
    {
        $digitsOnly = preg_replace('/[^0-9]/', '', $phone);
        if (strlen($digitsOnly) === 10) {
            $normalized = '+1' . $digitsOnly;
        } elseif (strlen($digitsOnly) === 11 && str_starts_with($digitsOnly, '1')) {
            $normalized = '+'. $digitsOnly;
        } else {
            // Invalid length for US phone number
            return null;
        }

        return $normalized;
    }

    /**
     * @param Consumer $consumer
     * @return bool
     */
    public function validateConsumerPhone(
        Consumer $consumer,
    ): bool
    {
        $acceptedClassifications = [
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS,
        ];

        return in_array($consumer->classification, $acceptedClassifications);
    }

    public function validSendingTime(
        ?string $stateAbbreviation = null,
    ): bool
    {
        $end = '20:55:00'; //9pm is deadline to send marketing sms, 8:55pm accounts for time taken to send

        if ($stateAbbreviation && StateAbbreviation::tryFrom($stateAbbreviation)) {
            $timezone = StateAbbreviation::timeZone($stateAbbreviation);
            $start = '08:00:00';
        } else {
            $timezone = StateAbbreviation::timeZone(StateAbbreviation::NY->value);
            $start = '11:00:00';
        }

        $start = Carbon::parse($start, $timezone);
        $end = Carbon::parse($end, $timezone);
        $now = Carbon::now($timezone);

        return $now->between($start, $end);
    }
}
