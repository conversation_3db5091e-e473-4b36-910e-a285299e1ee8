<?php

namespace App\Services;

use App\Enums\Calendar\CheckInStatus;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CheckIn;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;
use App\Models\User;
use App\Repositories\CheckIn\CheckInRepository;
use App\Services\Filterables\SalesOverview\CheckInFilterableService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CheckInService
{
    public const string     WORD_CHECK_IN          = 'check in';
    public const int        TEN_MINUTES_IN_SECONDS = 600;

    public function __construct(protected CheckInRepository $checkInRepository)
    {

    }

    /**
     * @param CalendarEvent $calendarEvent
     * @return bool
     */
    public function checkIfEventIsCheckIn(CalendarEvent $calendarEvent): bool
    {
        $pattern = '/\bcheck\s*[-]?\s*in\b/i';

        $title = $calendarEvent->{CalendarEvent::FIELD_TITLE};
        $description = $calendarEvent->{CalendarEvent::FIELD_DESCRIPTION};

        return Str::match($pattern, $title) || Str::match($pattern, $description);
    }


    /**
     * @param int $calendarEventId
     * @param string $status
     * @param int $userId
     * @return CheckIn
     */
    public function updateOrCreate(
        int $calendarEventId,
        string $status,
        int $userId,
    ): CheckIn
    {
        return $this->checkInRepository->updateOrCreate(
            calendarEventId: $calendarEventId,
            status         : $status,
            userId         : $userId
        );
    }

    /**
     * @param int|null $userId
     * @param array $filters
     * @param int|null $companyId
     *
     * @return Builder
     */
    public function getFilteredCheckInQuery(?int $userId, array $filters, ?int $companyId = null): Builder
    {
        $filterableService = app(CheckInFilterableService::class);
        $query = $this->getBaseCheckInQuery(userId: $userId, hasExternal: true);

        if ($companyId) {
            $query->whereHas(CheckIn::RELATION_COMPANY, fn(Builder $query) => $query->where(CheckIn::FIELD_COMPANY_ID, $companyId));
        }

        return $filterableService->runQuery($filters, $query)
            ->orderBy(CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_START_TIME, 'desc');
    }

    /**
     * @param int|null $userId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param bool|null $hasExternal
     * @return Builder
     */
    protected function getBaseCheckInQuery(
        ?int $userId = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?bool $hasExternal = null
    ): Builder
    {
        return CheckIn::query()
            ->select([
                CheckIn::TABLE. '.*',
                User::TABLE. '.' . User::FIELD_NAME . ' as user_name',
                DB::raw('SUM(' . ConferenceParticipant::TABLE. '.' . ConferenceParticipant::FIELD_DURATION_IN_SECONDS .') AS duration_in_call_in_seconds'),
                DB::raw("CASE WHEN EXISTS (
                        SELECT 1
                        FROM conference_participants
                        LEFT JOIN users ON users.name = conference_participants.name
                        WHERE conference_participants.conference_id = conferences.id
                        AND users.id IS NULL
                    ) THEN 1 ELSE 0 END as has_external_participants")
            ])
            ->with([
                CheckIn::RELATION_CALENDAR_EVENT
                . '.' . CalendarEvent::RELATION_CONFERENCES
                . '.' . Conference::RELATION_PARTICIPANTS,
                CheckIn::RELATION_CALENDAR_EVENT
                . '.' . CalendarEvent::RELATION_CONFERENCES
                . '.' . Conference::RELATION_TRANSCRIPTS
                . '.' . ConferenceTranscript::RELATION_ENTRIES
                . '.' . ConferenceTranscriptEntry::RELATION_PARTICIPANT,
                CheckIn::RELATION_CALENDAR_EVENT
                . '.' . CalendarEvent::RELATION_ATTENDEES
            ])
            ->when(filled($userId), fn($query) => $query->where(User::TABLE. '.' . User::FIELD_ID, $userId))
            ->join(CalendarEvent::TABLE, CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID, CheckIn::TABLE . "." . CheckIn::FIELD_CALENDAR_EVENT_ID)
            ->join(Conference::TABLE, Conference::TABLE . '.' . Conference::FIELD_CALENDAR_EVENT_ID, CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID)
            ->join(ConferenceParticipant::TABLE, ConferenceParticipant::TABLE . '.' . ConferenceParticipant::FIELD_CONFERENCE_ID, Conference::TABLE . '.' . Conference::FIELD_ID)
            ->join(User::TABLE, User::TABLE . '.' . User::FIELD_NAME, ConferenceParticipant::TABLE . '.' . ConferenceParticipant::FIELD_NAME)
            ->when(filled($startDate), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_START_TIME, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_END_TIME, '<=', $endDate))
            ->when(isset($hasExternal), fn($query) => $query->having('has_external_participants', $hasExternal))
            ->groupBy(
                Conference::TABLE . '.' . Conference::FIELD_CALENDAR_EVENT_ID,
                User::TABLE . '.' . User::FIELD_ID,
            );
    }
}
