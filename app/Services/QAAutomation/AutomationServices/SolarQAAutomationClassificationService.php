<?php

namespace App\Services\QAAutomation\AutomationServices;

use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\QAAutomation\QAAutomationLog;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\GlobalConfigurationService;
use Arr;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Collection;
use Exception;

class SolarQAAutomationClassificationService
{
    const string CLASSIFIER_MODEL = 'classifier_model';
    const string CLASSIFIER_CONFIDENCE_PERCENTAGE = 'classifier_confidence_percentage';

    const string SHADE_CLASSIFIER_MODEL = 'shade_classifier_model';
    const string SHADE_CLASSIFIER_CONFIDENCE_PERCENTAGE = 'shade_classifier_confidence_percentage';
    const string SOLAR_CLASSIFIER_MODEL = 'solar_classifier_model';
    const string SOLAR_CLASSIFIER_CONFIDENCE_PERCENTAGE = 'solar_classifier_confidence_percentage';
    const string MOBILE_HOME_CLASSIFIER_MODEL = 'mobile_home_classifier_model';
    const string MOBILE_HOME_CLASSIFIER_CONFIDENCE_PERCENTAGE = 'mobile_home_classifier_confidence_percentage';
    private string $apiUrl;
    private string $apiKey;
    private int $timeout;
    
    // Legacy single classifier configuration (fallback)
    private ?string $classifierModel;
    private ?float $classifierConfidence;
    
    // Shade classifier configuration
    private ?string $shadeClassifierModel;
    private ?float $shadeClassifierConfidence;
    
    // Solar classifier configuration
    private ?string $solarClassifierModel;
    private ?float $solarClassifierConfidence;
    
    // Mobile home classifier configuration
    private ?string $mobileHomeClassifierModel;
    private ?float $mobileHomeClassifierConfidence;
    
    private ConsumerProductRepository $consumerProductRepository;
    private GlobalConfigurationService $globalConfigurationService;

    public function __construct(ConsumerProductRepository $consumerProductRepository, GlobalConfigurationService $globalConfigurationService)
    {
        // Configure these in your .env file
        $this->apiUrl = config('services.solar_classifier.url');
        $this->apiKey = config('services.solar_classifier.api_key');
        $this->timeout = 30;
        $this->consumerProductRepository = $consumerProductRepository;
        $this->globalConfigurationService = $globalConfigurationService;
        
        // Get all solar classifier configuration
        $solarClassifierConfig = $globalConfigurationService->getConfigData(GlobalConfigurationKey::SOLAR_CLASSIFIER);
        
        // Legacy single classifier configuration (fallback)
        $this->classifierModel = Arr::get($solarClassifierConfig, self::CLASSIFIER_MODEL);
        $this->classifierConfidence = Arr::get($solarClassifierConfig, self::CLASSIFIER_CONFIDENCE_PERCENTAGE);
        
        // Shade classifier configuration
        $this->shadeClassifierModel = Arr::get($solarClassifierConfig, self::SHADE_CLASSIFIER_MODEL);
        $this->shadeClassifierConfidence = Arr::get($solarClassifierConfig, self::SHADE_CLASSIFIER_CONFIDENCE_PERCENTAGE);
        
        // Solar classifier configuration
        $this->solarClassifierModel = Arr::get($solarClassifierConfig, self::SOLAR_CLASSIFIER_MODEL);
        $this->solarClassifierConfidence = Arr::get($solarClassifierConfig, self::SOLAR_CLASSIFIER_CONFIDENCE_PERCENTAGE);
        
        // Mobile home classifier configuration
        $this->mobileHomeClassifierModel = Arr::get($solarClassifierConfig, self::MOBILE_HOME_CLASSIFIER_MODEL);
        $this->mobileHomeClassifierConfidence = Arr::get($solarClassifierConfig, self::MOBILE_HOME_CLASSIFIER_CONFIDENCE_PERCENTAGE);
    }
    public function qualifySolarLead(ConsumerProduct $consumerProduct): bool
    {
        /**
         * Criteria to qualify a solar lead from calculator inputs
         *
         *
         * Power bill above 80
         * How much shade is on your roof during the hours of 10 am to 3 pm - 'none'
         * Do you currently own or have authority with respect to this house? - 'yes'
         * Please describe this home - Freestanding
         *
         * Do you currently have solar panels on your home? - 'NO'
         * OR
         * Do you currently have solar panels on your home? - 'Yes'
         * +
         *  a. Currently only for hot water, looking for PV ||
         *  b. I am looking for battery storage ||
         *  c. I want to expand my solar system size ||
         */

        $consumerProductDataPayload = $consumerProduct->consumerProductData->payload;

        if (
            (int)Arr::get($consumerProductDataPayload, SolarConfigurableFields::ELECTRIC_COST->value) > 80 &&
            Arr::get($consumerProductDataPayload, SolarConfigurableFields::HOME_TYPE->value) === 'freestanding' &&
            // if pricing then 'yes' solar or battery
            (
                Arr::get($consumerProductDataPayload, SolarConfigurableFields::SOLAR_NEEDS->value) !== 'pricing' &&
                Arr::get($consumerProductDataPayload, SolarConfigurableFields::SOLAR_NEEDS->value) !== 'battery_storage'
            )
        ) {
            //check if they dont have solar
            if(
                Arr::get($consumerProductDataPayload, SolarConfigurableFields::CURRENT_SOLAR->value) === 'no' &&
                $this->hasExistingSolarArray($consumerProduct)
            ) {
                QAAutomationLog::create([
                    QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    QAAutomationLog::FIELD_ENTRY => 'Solar lead failed QA',
                    QAAutomationLog::FIELD_ERROR_MESSAGE => 'Solar Api return a match for an existing solar system',
                    QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
                ]);

                return false;
            }

            if(!$this->qualifyViaImageClassificationForSolar($consumerProduct)) {
                return false;
            }

            return true;

        } else {
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_ENTRY => 'Solar lead failed QA',
                QAAutomationLog::FIELD_ERROR_MESSAGE => json_encode($consumerProductDataPayload),
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
            ]);
        }

        return false;
    }

    /*
     * todo once solar gate can be trusted we can remove this call
     */
    protected function hasExistingSolarArray(ConsumerProduct $consumerProduct): bool
    {
        // Check if we already have a Google Solar API response
        $existingResponse = Arr::get($consumerProduct->consumerProductData?->payload, 'google_solar_api_response');

        if ($existingResponse && isset($existingResponse['hasInstallations'])) {
            return $existingResponse['hasInstallations'];
        }

        $requestParams = [
            'location.latitude' => $consumerProduct->address->latitude,
            'location.longitude' => $consumerProduct->address->longitude,
            'key' => config('services.google.solar.api_key')
        ];

        $response = Http::get(config('services.google.solar.base_url'), $requestParams);

        if ($response->successful()) {
            $responseData = $response->json();

            // Save the Google Solar API response to consumer product data
            $this->saveApiResponseToConsumerProduct(
                'google_solar_api_response',
                $responseData,
                $consumerProduct,
                'Google Solar API response'
            );

            return Arr::get($responseData, 'hasInstallations');
        } else {
            logger()->error("Couldn't retrieve phone data from Google Solar Api");
        }

        //return true to be safe because the api call has failed
        return true;
    }

    public function qualifyViaImageClassificationForSolar(ConsumerProduct $consumerProduct): bool
    {
        try {
            // Check if we already have a solar classification API response
            $existingResponse = Arr::get($consumerProduct->consumerProductData?->payload, 'solar_classification_api_response');

            if ($existingResponse) {
                // Check for legacy single model format
                if (isset($existingResponse['classification'])) {
                    return $this->evaluateSolarSuitability($existingResponse['classification'], $consumerProduct);
                }

                // Check for hierarchical format - re-run hierarchical logic if any gate results exist
                if (isset($existingResponse['shade_classifier']) ||
                    isset($existingResponse['solar_classifier']) ||
                    isset($existingResponse['mobile_home_classifier'])) {
                    return $this->evaluateHierarchicalResponse($existingResponse, $consumerProduct);
                }
            }

            $latitude = $consumerProduct->address->latitude;
            $longitude = $consumerProduct->address->longitude;
            
            if (!$latitude || !$longitude) {
                QAAutomationLog::create([
                    QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    QAAutomationLog::FIELD_ENTRY => 'Solar image classification failed',
                    QAAutomationLog::FIELD_ERROR_MESSAGE => 'Missing latitude or longitude coordinates',
                    QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
                ]);
                return false;
            }

            $result = $this->callSolarClassificationAPI($latitude, $longitude, $consumerProduct);
            
            if (!$result['success']) {
                QAAutomationLog::create([
                    QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    QAAutomationLog::FIELD_ENTRY => 'Solar image classification failed',
                    QAAutomationLog::FIELD_ERROR_MESSAGE => $result['error'] ?? 'Unknown error',
                    QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
                ]);
                return false;
            }

            $classification = $result['classification'];
            
            // Save the API response to consumer product data
            $this->saveApiResponseToConsumerProduct(
                'solar_classification_api_response',
                $result,
                $consumerProduct,
                'solar classification API response'
            );

            $logData = [
                'detailed_class' => $classification['detailed_class'],
                'confidence' => $classification['confidence'],
                'solar_suitable' => $classification['solar_suitable'],
                'coordinates' => $result['coordinates']
            ];
            
            // Add hierarchical gates info if available
            if (isset($result['hierarchical_gates'])) {
                $logData['classification_method'] = 'hierarchical_triple_gate';
                $logData['gates'] = [];
                
                $gates = $this->getGateConfiguration();
                foreach ($gates as $gateKey => $gateConfig) {
                    $gateLogKey = $gateKey . '_gate';
                    if (isset($result['hierarchical_gates'][$gateLogKey]) && $result['hierarchical_gates'][$gateLogKey]) {
                        $logData['gates'][$gateLogKey] = [
                            'class' => $result['hierarchical_gates'][$gateLogKey]['classification']['detailed_class'] ?? null,
                            'confidence' => $result['hierarchical_gates'][$gateLogKey]['classification']['confidence'] ?? null
                        ];
                    }
                }
            }
            
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_ENTRY => 'Solar image classification completed',
                QAAutomationLog::FIELD_ERROR_MESSAGE => json_encode($logData),
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
            ]);

            return $this->evaluateSolarSuitability($classification, $consumerProduct);

        } catch (Exception $e) {
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_ENTRY => 'Solar image classification error',
                QAAutomationLog::FIELD_ERROR_MESSAGE => $e->getMessage(),
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
            ]);

            return false;
        }
    }

    /**
     * Call the solar classification API for coordinates
     *
     * @param float $latitude
     * @param float $longitude
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    private function callSolarClassificationAPI(float $latitude, float $longitude, ConsumerProduct $consumerProduct): array
    {
        try {
            $this->validateCoordinates($latitude, $longitude);

            // Check if any hierarchical models are configured
            if ($this->shadeClassifierModel || $this->solarClassifierModel || $this->mobileHomeClassifierModel) {
                return $this->hierarchicalClassification($latitude, $longitude, $consumerProduct);
            }

            // Fall back to legacy single model if configured
            if (!empty($this->classifierModel)) {
                $result = $this->classifyWithModel($latitude, $longitude, $this->classifierModel, 'legacy_classifier', $consumerProduct);
                return $result;
            }

            throw new Exception("No classification models configured");

        } catch (Exception $e) {
            Logger()->error("Solar classification API error", [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'coordinates' => ['latitude' => $latitude, 'longitude' => $longitude]
            ];
        }
    }

    /**
     * Get gate configuration for hierarchical classification
     * 
     * @return array
     */
    private function getGateConfiguration(): array
    {
        return [
            'shade' => [
                'model' => $this->shadeClassifierModel,
                'confidence' => $this->shadeClassifierConfidence,
                'targetClass' => 'shade'
            ],
            'solar' => [
                'model' => $this->solarClassifierModel,
                'confidence' => $this->solarClassifierConfidence,
                'targetClass' => 'solar'
            ],
            'mobile_home' => [
                'model' => $this->mobileHomeClassifierModel,
                'confidence' => $this->mobileHomeClassifierConfidence,
                'targetClass' => 'mobile_home'
            ]
        ];
    }

    /**
     * Perform hierarchical triple-gate classification
     * Order: Shade -> Solar -> Mobile Home
     *
     * @param float $latitude
     * @param float $longitude
     * @param ConsumerProduct $consumerProduct
     * @return array
     * @throws ConnectionException
     */
    private function hierarchicalClassification(float $latitude, float $longitude, ConsumerProduct $consumerProduct): array
    {
        $finalClass = 'good';
        $gatesProcessed = [];
        $gates = $this->getGateConfiguration();

        // Process gates in order
        foreach ($gates as $gateKey => $gateConfig) {
            if (!$gateConfig['model']) {
                continue; // Skip if model not configured
            }
            
            $result = $this->classifyWithModel(
                $latitude, 
                $longitude, 
                $gateConfig['model'], 
                $gateKey . '_classifier', 
                $consumerProduct
            );

            if (!$result['success']) {
                return $result;
            }
            
            $gatesProcessed[$gateKey] = $result;
            
            // Check if confidence threshold is met (if configured)
            if ($this->evaluateGateResult($result, $gateConfig['targetClass'], $gateConfig['confidence'])) {
                $finalClass = $gateConfig['targetClass'];
                $finalConfidence = $result['classification']['confidence'];
                return $this->formatHierarchicalResult($finalClass, $finalConfidence, $latitude, $longitude, $gatesProcessed);
            }
        }

        // Calculate conservative confidence for 'good' classification
        $finalConfidence = $this->calculateConservativeConfidence($gatesProcessed);

        return $this->formatHierarchicalResult($finalClass, $finalConfidence, $latitude, $longitude, $gatesProcessed);
    }

    /**
     * Classify using a specific model and save to consumer product data
     *
     * @param float $latitude
     * @param float $longitude
     * @param string $model
     * @param string $modelKey Key to save response under
     * @param ConsumerProduct $consumerProduct
     * @return array
     * @throws ConnectionException
     */
    private function classifyWithModel(float $latitude, float $longitude, string $model, string $modelKey, ConsumerProduct $consumerProduct): array
    {
        try {
            $headers = [
                'X-API-Key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            $requestData = [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'model' => $model
            ];

            $response = Http::timeout($this->timeout)
                ->withHeaders($headers)
                ->post("{$this->apiUrl}/classify", $requestData);

            if (!$response->successful()) {
                throw new Exception("API request failed with status: " . $response->status() . " - " . $response->body());
            }

            $data = $response->json();

            if (isset($data['status']) && $data['status'] === 'error') {
                throw new Exception("Classification failed: " . ($data['error'] ?? 'Unknown error'));
            }

            $result = $this->formatClassificationResult($data, $latitude, $longitude);
            
            // Save this model's response to consumer product data
            $this->saveModelResponse($modelKey, $result, $consumerProduct);
            
            return $result;

        } catch (Exception $e) {
            throw $e;
        }
    }
    
    /**
     * Evaluate if a gate result meets the criteria
     *
     * @param array $result
     * @param string $targetClass
     * @param float|null $confidenceThreshold
     * @return bool
     */
    private function evaluateGateResult(array $result, string $targetClass, ?float $confidenceThreshold): bool
    {
        $class = $result['classification']['detailed_class'] ?? '';
        $confidence = $result['classification']['confidence'] ?? 0;
        
        // If no confidence threshold is set, just check the class
        if ($confidenceThreshold === null) {
            return $class === $targetClass;
        }
        
        // Check both class and confidence threshold
        return $class === $targetClass && $confidence >= $confidenceThreshold;
    }
    
    /**
     * Calculate conservative confidence for 'good' classification
     *
     * @param array $gatesProcessed
     * @return float
     */
    private function calculateConservativeConfidence(array $gatesProcessed): float
    {
        $confidences = [];
        
        foreach ($gatesProcessed as $gate => $result) {
            $class = $result['classification']['detailed_class'] ?? '';
            $confidence = $result['classification']['confidence'] ?? 100;
            
            // For 'good' classifications, use the confidence directly
            // For non-good classifications, use inverse confidence
            if ($class === 'good') {
                $confidences[] = $confidence;
            } else {
                $confidences[] = 100 - $confidence;
            }
        }
        
        // Return the minimum confidence (most conservative)
        return !empty($confidences) ? min($confidences) : 100.0;
    }
    
    /**
     * Save API response data to consumer product payload
     *
     * @param string $key
     * @param array $data
     * @param ConsumerProduct $consumerProduct
     * @param string $errorContext
     * @return void
     */
    private function saveApiResponseToConsumerProduct(string $key, array $data, ConsumerProduct $consumerProduct, string $errorContext): void
    {
        try {
            $apiResponseData = new Collection([$key => $data]);
            $this->consumerProductRepository->updateConsumerProductModelAndPayloadById(
                $consumerProduct->id,
                $apiResponseData,
                [], // No model keys to update
                [$key] // Save to payload
            );
        } catch (Exception $e) {
            Logger()->error("Failed to save {$errorContext}", [
                'consumer_product_id' => $consumerProduct->id,
                'key' => $key,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Save model response to consumer product data
     *
     * @param string $modelKey
     * @param array $response
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    private function saveModelResponse(string $modelKey, array $response, ConsumerProduct $consumerProduct): void
    {
        $this->saveApiResponseToConsumerProduct(
            "solar_classification_api_response.{$modelKey}",
            $response,
            $consumerProduct,
            "{$modelKey} classification API response"
        );
    }

    /**
     * Evaluate existing hierarchical response without making new API calls
     *
     * @param array $existingResponse
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    private function evaluateHierarchicalResponse(array $existingResponse, ConsumerProduct $consumerProduct): bool
    {
        $gates = $this->getGateConfiguration();
        
        // Process gates in order
        foreach ($gates as $gateKey => $gateConfig) {
            $classifierKey = $gateKey . '_classifier';
            
            if (isset($existingResponse[$classifierKey])) {
                $gateResult = $existingResponse[$classifierKey];
                if ($this->evaluateGateResult($gateResult, $gateConfig['targetClass'], $gateConfig['confidence'])) {
                    return $this->evaluateSolarSuitability($gateResult['classification'], $consumerProduct);
                }
            }
        }

        // Default to good - evaluate using conservative logic
        $gates = $this->getGateConfiguration();
        $gatesProcessed = [];
        foreach ($gates as $gateKey => $gateConfig) {
            $classifierKey = $gateKey . '_classifier';
            if (isset($existingResponse[$classifierKey])) {
                $gatesProcessed[$gateKey] = $existingResponse[$classifierKey];
            }
        }
        
        $finalConfidence = $this->calculateConservativeConfidence($gatesProcessed);
        $goodClassification = [
            'detailed_class' => 'good',
            'confidence' => $finalConfidence,
            'solar_suitable' => true
        ];
        
        return $this->evaluateSolarSuitability($goodClassification, $consumerProduct);
    }

    /**
     * Evaluate if the classification result indicates solar suitability
     *
     * @param array $classification
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    private function evaluateSolarSuitability(array $classification, ConsumerProduct $consumerProduct): bool
    {
        // For hierarchical classifications, confidence already checked at gate level
        // For legacy single model classifications, apply confidence threshold
        if (!isset($classification['classification_method']) || $classification['classification_method'] !== 'hierarchical_triple_gate') {
            // Must have reasonable confidence for legacy single model
            if ($classification['confidence'] < $this->classifierConfidence ?? 90) {
                return false;
            }
        }

        // 'good' classes are acceptable for solar installation
        // 'shade', 'solar', 'mobile_home' are not suitable
        $acceptableClasses = ['good'];

        //if consumer is 'willing to trim trees' add shade
        if(Arr::get($consumerProduct->consumerProductData?->payload, SolarConfigurableFields::SHADE_NOTES->value) === 'Willing to trim trees') {
            $acceptableClasses[] = 'shade';
        }

        //if they want to increase or have hot water approve with solar
        if(
            Arr::get($consumerProduct->consumerProductData?->payload, SolarConfigurableFields::SOLAR_NEEDS->value) === 'increase_system' ||
            Arr::get($consumerProduct->consumerProductData?->payload, SolarConfigurableFields::SOLAR_NEEDS->value) === 'pv'
        ) {
            $acceptableClasses[] = 'solar';
        }
        
        return in_array($classification['detailed_class'], $acceptableClasses);
    }

    /**
     * Validate latitude and longitude coordinates
     *
     * @param float $latitude
     * @param float $longitude
     * @throws Exception
     */
    private function validateCoordinates(float $latitude, float $longitude): void
    {
        if ($latitude < -90 || $latitude > 90) {
            throw new Exception("Invalid latitude: {$latitude}. Must be between -90 and 90.");
        }

        if ($longitude < -180 || $longitude > 180) {
            throw new Exception("Invalid longitude: {$longitude}. Must be between -180 and 180.");
        }
    }

    /**
     * Format the API response into a standardized result
     *
     * @param array $apiResponse
     * @param float $latitude
     * @param float $longitude
     * @return array Formatted result
     */
    private function formatClassificationResult(array $apiResponse, float $latitude, float $longitude): array
    {
        return [
            'success' => true,
            'coordinates' => [
                'latitude' => $latitude,
                'longitude' => $longitude
            ],
            'classification' => [
                'detailed_class' => $apiResponse['predictions']['detailed']['class'] ?? null,
                'confidence' => $apiResponse['predictions']['detailed']['confidence'] ?? 0,
                'binary_class' => $apiResponse['predictions']['binary']['suitable'] ?? null,
                'solar_suitable' => $apiResponse['predictions']['binary']['suitable'] ?? false,
                'suitability_probability' => $apiResponse['predictions']['binary']['confidence'] ?? 0
            ],
            'probabilities' => $apiResponse['predictions']['detailed']['probabilities'] ?? [],
            'processing_time_seconds' => $apiResponse['processing_time_seconds'] ?? null,
            'model_info' => $apiResponse['model_info'] ?? null,
            'classified_at' => now()->toISOString()
        ];
    }

    /**
     * Format hierarchical classification result
     *
     * @param string $finalClass
     * @param float $finalConfidence
     * @param float $latitude
     * @param float $longitude
     * @param array $gateResults
     * @return array
     */
    private function formatHierarchicalResult(string $finalClass, float $finalConfidence, float $latitude, float $longitude, array $gateResults): array
    {
        $solarSuitable = $finalClass === 'good';
        
        return [
            'success' => true,
            'coordinates' => [
                'latitude' => $latitude,
                'longitude' => $longitude
            ],
            'classification' => [
                'detailed_class' => $finalClass,
                'confidence' => $finalConfidence,
                'binary_class' => $solarSuitable ? 'suitable' : 'unsuitable',
                'solar_suitable' => $solarSuitable,
                'suitability_probability' => $solarSuitable ? $finalConfidence : (100 - $finalConfidence),
                'classification_method' => 'hierarchical_triple_gate'
            ],
            'hierarchical_gates' => $gateResults,
            'classification_method' => 'hierarchical_triple_gate',
            'classified_at' => now()->toISOString()
        ];
    }
}