<?php

namespace App\Services\QAAutomation;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessor;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\QAAutomation\QAAutomationIndustryService;
use App\Models\QAAutomation\QAAutomationLog;
use App\Models\User;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\QAAutomation\AutomationServices\PhoneUnverifiedQAAutomation;
use App\Services\QAAutomation\AutomationServices\RegexQAAutomation;
use App\Services\QAAutomation\AutomationServices\SolarQAAutomationClassificationService;
use Arr;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Http;
use Throwable;

class QAAutomationService
{
    const string MAX_CONTACT_DELAY_SECONDS = 'max_contact_delay_seconds';

    /**
     * @param RegexQAAutomation $regexService
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ProductProcessingService $productProcessingService
     * @param PhoneUnverifiedQAAutomation $phoneUnverifiedQAAutomationService
     */
    public function __construct(
        protected RegexQAAutomation $regexService,
        protected ConsumerProductRepository $consumerProductRepository,
        protected ProductProcessingService $productProcessingService,
        protected PhoneUnverifiedQAAutomation $phoneUnverifiedQAAutomationService,
        protected SolarQAAutomationClassificationService $solarQAAutomationClassificationService,
    ) {}

    /**
     * Main Entry for Automated QA Process for Leads
     * Returns true if automation is configured, enabled, and lead passes checks - false otherwise
     * @param int $consumerProductId
     * @return bool
     */
    public function qualifyConsumerProduct(int $consumerProductId): bool
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::find($consumerProductId);
        // Validate consumer is sms verified with required fields
        $consumer = $consumerProduct->consumer;

        // Get industry service for lead
        $industryService = $consumerProduct->industryService;

        // Get QA automation configurations for industry service
        $qaConfigs = QAAutomationIndustryService::query()
            ->where(QAAutomationIndustryService::FIELD_INDUSTRY_SERVICE_ID, $industryService->{IndustryService::FIELD_ID})
            ->where(QAAutomationIndustryService::FIELD_ENABLED, true)
            ->get();

        // If automation is not enabled for industry service return
        if ($qaConfigs->isEmpty()){
            return false;
        }

        $allowUnverified = $qaConfigs->where(QAAutomationIndustryService::FIELD_TYPE, QAAutomationIndustryService::TYPE_PHONE_UNVERIFIED)->isNotEmpty();

        if(is_null($consumerProduct->status)) {
            //log for creation race condition
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
                QAAutomationLog::FIELD_ENTRY => "Consumer Product Status is null"
            ]);

            return false;
        }

        if (!$consumer->first_name ||
            !$consumer->last_name ||
            !$consumer->email ||
            $consumerProduct->own_property !== 'yes' ||
            $consumerProduct->status !== ConsumerProduct::STATUS_INITIAL ||
            !$consumer->formatted_phone ||
            !(
                ($allowUnverified && $consumer->classification === Consumer::CLASSIFICATION_UNVERIFIED_PHONE)
                || $consumer->classification === Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS
            )
        )
        {
            return false;
        }

        // Run each QA Automation Configuration
        foreach ($qaConfigs as $qaConfig) {
            // Regex check
            if ($qaConfig->{QAAutomationIndustryService::FIELD_TYPE} === QAAutomationIndustryService::TYPE_REGEX) {
                // If Failed regex check, return - log created in regex service
                if (!$this->regexService->qualifyConsumerProduct($consumerProduct))
                    return false;

                //special rules for solar leads only
                if(
                    $consumerProduct->industryService->industry->name === IndustryEnum::SOLAR->value
                ) {
                    if(!$this->solarQAAutomationClassificationService->qualifySolarLead($consumerProduct))
                        return false;
                }
            }
            // Unverified check
            if (
                $consumer->classification === Consumer::CLASSIFICATION_UNVERIFIED_PHONE &&
                $qaConfig->{QAAutomationIndustryService::FIELD_TYPE} === QAAutomationIndustryService::TYPE_PHONE_UNVERIFIED
            ) {
                if(!$this->phoneUnverifiedQAAutomationService->qualifyConsumerProduct($consumerProduct))
                    return false;
            }
        }

        // If all checks pass, return true
        return true;
    }

    /**
     * @param int $consumerProductId
     * @return void
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function processLead(int $consumerProductId): void
    {
        $consumerProduct = $this->consumerProductRepository->findOrFail($consumerProductId);

        // Confirm lead status is in initial
        if ($consumerProduct->status !== ConsumerProduct::STATUS_INITIAL) {
            return;
        }

        $this->checkNotReservedAndReserveProduct($consumerProduct);

        $this->consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_PENDING_ALLOCATION);

        $this->productProcessingService->approveProduct(
            consumerProduct: $consumerProduct,
            processor: LeadProcessor::systemProcessor(),
            reason: "Automated QA",
        );

        $this->consumerProductRepository->markConsumerProductGoodToSell($consumerProduct);

        QAAutomationLog::create([
            QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            QAAutomationLog::FIELD_ENTRY => 'Consumer Product Passed QA',
            QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_APPROVE,
        ]);
    }

    public function checkNotReservedAndReserveProduct(ConsumerProduct $consumerProduct)
    {
        // Confirm lead is not locked by the system
        $reserved = LeadProcessingReservedLead::query()
            ->join(LeadProcessor::TABLE, LeadProcessor::TABLE . '.' . LeadProcessor::FIELD_ID, '=', LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_PROCESSOR_ID)
            ->join(User::TABLE, User::TABLE . '.' . User::FIELD_ID, '=', LeadProcessor::TABLE . '.' . LeadProcessor::FIELD_USER_ID)
            ->where(LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->whereNot(LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_PROCESSOR_ID, LeadProcessor::systemProcessor()->id)
            ->get([
                User::TABLE . '.' . User::FIELD_ID,
                User::TABLE . '.' . User::FIELD_NAME,
            ])
            ->first();

        if ($reserved) {
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
                QAAutomationLog::FIELD_ENTRY => "Attempted to qualify lead reserved by {$reserved->{User::FIELD_NAME}} ({$reserved->{User::FIELD_ID}})",
            ]);
            return;
        }

        //reserve this product to the system user
        try {
            $this->productProcessingService->reserveProduct($consumerProduct, LeadProcessor::systemProcessor());
        } catch (\Exception $e) {
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
                QAAutomationLog::FIELD_ERROR_MESSAGE => $e->getMessage(),
                QAAutomationLog::FIELD_ENTRY => "Failed to reserve consumer product",
            ]);
        }
    }
}
