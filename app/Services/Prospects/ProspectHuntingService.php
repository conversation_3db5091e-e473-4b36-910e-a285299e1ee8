<?php

namespace App\Services\Prospects;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\RoleConfiguration;
use App\Models\User;
use App\Models\USZipCode;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use RuntimeException;
use Throwable;

class ProspectHuntingService
{
    const int   LEAD_BUYER_RECENCY_THRESHOLD_DAYS         = 120;
    const array EXISTING_COMPANIES_INDUSTRY_WHITELIST_IDS = [
        2, // roofing
        11, // windows
        12, // siding
        14, // bathrooms
        15, // kitchens
    ];

    /**
     * @param User $user
     * @param array $timezones
     * @param bool $reserve
     * @return NewBuyerProspect|null
     * @throws Exception
     */
    public static function getNextAvailableProspect(User $user, array $timezones = [], bool $reserve = true): ?NewBuyerProspect
    {
        /** @var NewBuyerProspect $prospect */
        $prospect = self::getInitialProspectQueueQuery(
            $timezones,
            $user->configurationForRole('industries', 'business-development-manager', [])
        )->first();

        if($prospect?->source == ProspectSource::REGISTRATION && $prospect?->contacts()->count() < 1){
            $prospect->attemptCreateContactFromSourceData();
        }

        if ($prospect && $reserve) {
            self::reserveProspect($prospect, $user);
        }

        return $prospect;
    }

    /**
     * @param User $user
     * @param bool $reserve
     * @return NewBuyerProspect|null
     * @throws Exception
     */
    public static function getNextAvailableRegistrationProspect(User $user, bool $reserve = true): ?NewBuyerProspect
    {
        $query    = self::getInitialRegistrationQueueQuery();
        $prospect = $query->first();

        if ($prospect && $reserve)
            self::reserveProspect($prospect, $user);

        return $prospect;
    }

    /**
     * @param array $timezones
     * @param array $industryIds
     * @return Builder
     */
    private static function getInitialProspectQueueQuery(array $timezones = [], array $industryIds = []): Builder
    {
        $query = NewBuyerProspect::query()
            ->selectRaw("new_buyer_prospects.*, if(json_extract(source_data, '$.verified'), true, false) as human_verified, if(json_extract(source_data, '$.targeted'), true, false) as targeted")
            ->whereStatus(ProspectStatus::INITIAL)
            ->whereSource(ProspectSource::SALESINTEL)
            ->whereHas('contacts')
            ->orderByRaw('released_at, human_verified desc, targeted desc');

        // joining on json field is not efficient. todo: prepopulate timezones into new_buyer_prospects table
        if (count($timezones) > 0) {
            $query->leftJoin(USZipCode::TABLE, function ($join) {
                $join->on(function ($on) {
                    $on->whereRaw("us_zip_codes.zip_code = json_unquote(json_extract(new_buyer_prospects.source_data, '$.zip_codes[0]'))");
                });
            })
                ->whereIn(USZipCode::TABLE . '.' . USZipCode::FIELD_TIME_ZONE, $timezones);
        }

        if (count($industryIds) > 0) {
            $serviceIds = IndustryService::query()->whereIn(IndustryService::FIELD_INDUSTRY_ID, $industryIds)->pluck(IndustryService::FIELD_ID)->toArray();
            $query->whereJsonOverlaps(NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS, $serviceIds);
        }

        return $query;
    }

    /**
     * @return Builder
     */
    private static function getInitialRegistrationQueueQuery(): Builder
    {
        return NewBuyerProspect::query()
            ->where(NewBuyerProspect::FIELD_STATUS, ProspectStatus::INITIAL)
            ->where(NewBuyerProspect::FIELD_USER_ID, 0)
            ->where(NewBuyerProspect::FIELD_SOURCE, ProspectSource::REGISTRATION)
            ->orderByRaw('-' . NewBuyerProspect::FIELD_ORDINAL_VALUE . ' desc');
    }

    /**
     * @param NewBuyerProspect $prospect
     * @param User $user
     * @return void
     * @throws Exception
     */
    private static function reserveProspect(NewBuyerProspect $prospect, User $user): void
    {
        if ($prospect->user_id !== $user->id && $prospect->isReserved()) {
            throw new RuntimeException('Attempt to reserve a prospect that has already been reserved.');
        }

        $prospect->update([
            NewBuyerProspect::FIELD_USER_ID => $user->id,
            NewBuyerProspect::FIELD_STATUS  => ProspectStatus::ACTIVE,
            NewBuyerProspect::FIELD_USER_ASSIGNED_AT => now()
        ]);
    }

    /**
     * @param User $user
     * @return Collection
     */
    public static function getProspectsByUserId(User $user): Collection
    {
        return NewBuyerProspect::query()
            ->with('contacts')
            ->where(NewBuyerProspect::FIELD_USER_ID, $user->id)
            ->where(NewBuyerProspect::FIELD_STATUS, ProspectStatus::ACTIVE)
            ->get();
    }

    /**
     * @param User $user
     * @param array $timezones
     * @param bool $reserve
     * @return Company|null
     * @throws Throwable
     */
    public static function getNextAvailableExistingCompany(User $user, array $timezones = [], bool $reserve = true): ?Company
    {
        $filteredCompanyIds = self::getFilteredCompanyIds($timezones, $user->configurationForRole('industries', 'business-development-manager', []));
        $query              = self::getSortedCompanyQuery($filteredCompanyIds);
        $company            = $query->first();

        if ($company && $reserve) {
            BusinessDevelopmentManagerAssignmentService::assignToCompany($user->id, $company->id);
        }

        return $company;
    }

    /**
     * @param Builder $query
     * @param array $timezones
     * @param array $industryIds
     * @return Builder
     */
    private static function addCompanyFilters(Builder $query, array $timezones = [], array $industryIds = []): Builder
    {
        $query
            ->whereNot(Company::TABLE . '.' . Company::FIELD_ADMIN_STATUS, CompanyAdminStatus::COLLECTIONS)
            ->doesntHave('accountManager')
            ->doesntHave('businessDevelopmentManager')
            ->leftJoin(ProductAssignment::TABLE, function ($join) {
                $join->on(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                    ->whereDate(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays(self::LEAD_BUYER_RECENCY_THRESHOLD_DAYS));
            })
            ->whereNull(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID);

        // todo add timezone filtering
//        if (count($timezones) > 0) {
//            $query->leftJoin(USZipCode::TABLE, function ($join) {
//                $join->on(function ($on) {
//                    $on->whereRaw("us_zip_codes.zip_code = json_unquote(json_extract(new_buyer_prospects.source_data, '$.zip_codes[0]'))");
//                });
//            })
//                ->whereIn(USZipCode::TABLE . '.' . USZipCode::FIELD_TIME_ZONE, $timezones);
//        }

        if (count($industryIds) > 0) {
            $query->leftJoin(CompanyIndustry::TABLE, CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
                ->whereIn(CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_INDUSTRY_ID, $industryIds);
        }

        return $query;
    }

    /**
     * @param array $companyIds
     * @return Builder
     */
    private static function getSortedCompanyQuery(array $companyIds): Builder
    {
        $query = Company::query()
            ->selectRaw("companies.*, json_extract(company_data.payload, '$.google_review_count') reviews,
                if(json_extract(company_data.payload, '$.qr_top_500_company'), true, false) top_500,
                (select max(created_at) from product_assignments where company_id = companies.id and product_assignments.delivered = 1 and product_assignments.chargeable = 1) last_lead,
                (select max(created_at) from sales_intel_user_import_records where company_id = companies.id) last_user_import")
            ->whereIntegerInRaw(Company::TABLE . '.' . Company::FIELD_ID, $companyIds)
            ->leftJoin(CompanyData::TABLE, CompanyData::TABLE . '.' . CompanyData::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->orderByRaw("top_500 desc, last_lead desc, last_user_import desc, reviews desc");

        return $query;
    }

    /**
     * @param array $timezones
     * @param array $industryIds
     * @return array
     */
    private static function getFilteredCompanyIds(array $timezones = [], array $industryIds = []): array
    {
        $query = Company::query()
            ->select(Company::TABLE . '.*')
            ->whereNot(Company::TABLE . '.' . Company::FIELD_ADMIN_STATUS, CompanyAdminStatus::COLLECTIONS)
            ->whereNotIn('sales_status', CompanySalesStatus::disqualifyingStatuses())
            ->doesntHave('accountManager')
            ->doesntHave('businessDevelopmentManager')
            ->doesntHave('salesDevelopmentRepresentative')
            ->leftJoin(ProductAssignment::TABLE, function ($join) {
                $join->on(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                    ->whereDate(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays(self::LEAD_BUYER_RECENCY_THRESHOLD_DAYS));
            })
            ->leftJoin(CompanyIndustry::TABLE, CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->whereIn(CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_INDUSTRY_ID, self::EXISTING_COMPANIES_INDUSTRY_WHITELIST_IDS)
            ->whereNull(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID);

        if (count($industryIds) > 0) {
            $query->whereIn(CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_INDUSTRY_ID, $industryIds);
        }

        if (count($timezones) > 0) {
            $query->whereHas(Company::RELATION_PRIMARY_LOCATION . '.' . CompanyLocation::RELATION_ADDRESS . '.' . Address::RELATION_US_ZIP_CODE, function ($query) use ($timezones) {
                $query->whereIn(USZipCode::FIELD_TIME_ZONE, $timezones);
            });
        }

        return $query->pluck(Company::FIELD_ID)->toArray();
    }
}
