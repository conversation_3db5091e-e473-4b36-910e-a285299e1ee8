<?php

namespace App\Services\MarketingCampaign;

use App\Contracts\Marketing\MarketingEventInterpreterContract;
use App\Services\MarketingCampaign\Events\DeliveredEvent;
use App\Services\MarketingCampaign\Events\FailedEvent;
use App\Services\MarketingCampaign\Events\OpenedEvent;
use App\Services\MarketingCampaign\Events\QueuedEvent;
use App\Services\MarketingCampaign\Events\SentEvent;
use App\Services\MarketingCampaign\Events\SMSOptOutEvent;
use App\Services\MarketingCampaign\Events\SMSResponseEvent;
use App\Services\MarketingCampaign\Events\MarketingEvent;
use App\Services\MarketingCampaign\Events\ResubscribedEvent;
use Illuminate\Http\Request;

class TwilioEventInterpreter implements MarketingEventInterpreterContract
{
    const string MCC_ID = 'mcc_id';
    const string OPT_OUT_TYPE = 'OptOutType';
    const string FROM_PHONE_NUMBER = 'From';
    const string MESSAGE_STATUS = "MessageStatus";
    const string START_OPT_TYPE = 'START';
    const string STOP_OPT_TYPE = 'STOP';
    const string STATUS_SENT = 'sent';
    const string STATUS_FAILED = 'failed';
    const string STATUS_QUEUED = 'queued';
    const string STATUS_DELIVERED = 'delivered';
    const string STATUS_READ = 'read';
    const string SMS_STATUS = 'SmsStatus';
    const string ERROR_CODE = 'ErrorCode';
    const string ERROR_MESSAGE = 'ErrorMessage';

    /**
     * @param Request $request
     * @return MarketingEvent|null
     */
    public function interpret(Request $request): ?MarketingEvent
    {
        $marketingCampaignConsumerId = $request->input(self::MCC_ID);
        $optOutType = $request->input(self::OPT_OUT_TYPE);
        $event = null;

        $smsStatus = $request->input(self::SMS_STATUS);

        if ($smsStatus === 'received' && $optOutType === null) {
            $marketingCampaignConsumerService = app(MarketingCampaignConsumerService::class);

            $marketingCampaignConsumer = $marketingCampaignConsumerService->findMarketingCampaignConsumerByPhone(
                phone: $request->input(self::FROM_PHONE_NUMBER)
            );

            if ($marketingCampaignConsumer) {
                $event = new SMSResponseEvent($marketingCampaignConsumer->id, $request->all());
            }
        } else if ($optOutType) {
            if (empty($marketingCampaignConsumerId)) {
                $marketingCampaignConsumerService = app(MarketingCampaignConsumerService::class);

                $marketingCampaignConsumer = $marketingCampaignConsumerService->findMarketingCampaignConsumerByPhone(
                    phone: $request->input(self::FROM_PHONE_NUMBER)
                );

                $marketingCampaignConsumerId = $marketingCampaignConsumer?->id;
            }

            if ($marketingCampaignConsumerId) {
                $event = match ($optOutType) {
                    self::STOP_OPT_TYPE => new SMSOptOutEvent($marketingCampaignConsumerId, $request->input(self::FROM_PHONE_NUMBER)),
                    self::START_OPT_TYPE => new ResubscribedEvent($marketingCampaignConsumerId),
                    default => null,
                };
            }
        } else if ($marketingCampaignConsumerId) {
            $status = $request->input(self::MESSAGE_STATUS);

            $event = match ($status) {
                self::STATUS_DELIVERED => new DeliveredEvent($marketingCampaignConsumerId),
                self::STATUS_READ => new  OpenedEvent($marketingCampaignConsumerId),
                self::STATUS_QUEUED => new QueuedEvent($marketingCampaignConsumerId),
                self::STATUS_SENT => new SentEvent($marketingCampaignConsumerId),
                self::STATUS_FAILED => new FailedEvent($marketingCampaignConsumerId, $request->input(self::ERROR_CODE), $request->input(self::ERROR_MESSAGE)),
                default => null,
            };
        }

        return $event;
    }
}
