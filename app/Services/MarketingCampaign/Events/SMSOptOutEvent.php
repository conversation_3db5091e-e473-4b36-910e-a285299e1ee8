<?php

namespace App\Services\MarketingCampaign\Events;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\SMSOptOut\SmsOptOutService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Throwable;

class SMSOptOutEvent extends MarketingCampaignConsumerMarketingEvent
{
    public function __construct(
        int $marketingCampaignConsumerId,
        protected string $phone,
    )
    {
        parent::__construct($marketingCampaignConsumerId);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    function trigger(): void
    {
        $service = app(MarketingCampaignConsumerRepository::class);

        $mcc = $service->get(id: $this->marketingCampaignConsumerId);

        $optOutService = app(SmsOptOutService::class);

        try {
            $optOutService->add(
                phone: $this->phone,
                reason: 'Recipient Opted Out'
            );
        } catch (Throwable $exception) {
            MarketingLogService::log(
                message: 'Failed to add phone to opt out table',
                namespace: MarketingLogType::WEBHOOK,
                level: LogLevel::ERROR,
                context: [
                    'phone' => $this->phone,
                    'exception_message' => $exception->getMessage(),
                ],
                relations: [$mcc, $mcc->marketingCampaign],
            );
        }

        MarketingLogService::log(
            message: 'Consumer Unsubscribed',
            namespace: MarketingLogType::WEBHOOK,
            level: LogLevel::ALERT,
            context: [
                'phone' => $this->phone,
            ],
            relations: [$mcc, $mcc->marketingCampaign],
        );
    }
}
