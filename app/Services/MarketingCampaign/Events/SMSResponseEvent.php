<?php

namespace App\Services\MarketingCampaign\Events;

use App\Enums\CommunicationRelationTypes;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\Call;
use App\Repositories\CommunicationRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class SMSResponseEvent extends MarketingCampaignConsumerMarketingEvent
{
    public function __construct(
        int $marketingCampaignConsumerId,
        protected array $payload,
    )
    {
        parent::__construct($marketingCampaignConsumerId);
    }

    function trigger(): void
    {
        $communicationRepository = app(CommunicationRepository::class);

        $phone = $communicationRepository->getPhoneFromPhoneNumber(Arr::get($this->payload, 'To'));

        $text = $communicationRepository->updateOrCreateInboundSMS(
            serviceName: Call::EXTERNAL_TYPE_TWILIO,
            reference: Arr::get($this->payload, 'MessageSid'),
            fromNumber: Str::replace('+1', '', Arr::get($this->payload, 'From')),
            toPhoneId: $phone->id,
            body: Arr::get($this->payload, 'Body'),
            relType: CommunicationRelationTypes::MARKETING_CONSUMERS->value,
            relId: $this->marketingCampaignConsumerId,
        );

        $mcc = app(MarketingCampaignConsumerRepository::class)->findOrFail(marketingCampaignConsumerId: $this->marketingCampaignConsumerId);

        MarketingLogService::log(
            message: 'Consumer Responded',
            namespace: MarketingLogType::WEBHOOK,
            level: LogLevel::ALERT,
            context: [
                'payload' => $this->payload,
            ],
            relations: [$text, $mcc, $mcc->marketingCampaign]
        );
    }
}
