<?php

namespace App\Services\Filterables\SalesOverview;

use App\Models\Calendar\CheckIn;
use App\Services\Filterables\BaseDateRangeFilterableOption;

class CheckInDateFilterable extends BaseDateRangeFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model = CheckIn::class;
        $this->id = 'checkin-date-range';
        $this->minFromDate = null;

        parent::__construct();
    }

    protected function setTableAndColumn(): void
    {
        $this->tableAndColumn = CheckIn::TABLE .'.'. CheckIn::CREATED_AT;
    }
}
