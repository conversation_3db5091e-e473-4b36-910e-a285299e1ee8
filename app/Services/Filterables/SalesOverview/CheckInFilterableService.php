<?php

namespace App\Services\Filterables\SalesOverview;

use App\Models\Calendar\CheckIn;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class CheckInFilterableService extends BaseFilterableService
{
    const string FILTERABLE_CATEGORY = 'checkin-search';

    protected string $baseModel = CheckIn::class;

    protected array $filters = [
        CheckInDateFilterable::class,
    ];

    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? CheckIn::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
