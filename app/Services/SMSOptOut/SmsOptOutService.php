<?php

namespace App\Services\SMSOptOut;

use App\Enums\GlobalConfigurationKey;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Exceptions\CustomValidationException;
use App\Models\SmsOptOut;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\SMSOptOut\SmsOptOutRepository;
use App\Repositories\TextRepository;
use App\Services\PhoneNumber\PhoneNumberService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class SmsOptOutService
{
    public function __construct(
        protected SMSOptOutRepository $smsOptOutRepository,
        protected PhoneNumberService $phoneNumberService,
    )
    {
    }

    public function list(
        ?string $phone = null,
    )
    {
        return $this->smsOptOutRepository->list(
            phone: $phone,
        );
    }

    /**
     * @throws CustomValidationException
     */
    public function add(
        string $phone,
        string $reason,
    )
    {
        $e164 = $this->phoneNumberService->normalisePhoneNumber(phone: $phone);

        $exists = $this->smsOptOutRepository->exists(phone: $e164);

        if ($exists) {
            throw new CustomValidationException('Phone number already opted out.');
        }

        return $this->smsOptOutRepository->add(phone: $e164, reason: $reason);
    }

    public function delete(SmsOptOut $smsOptOut)
    {
        return $this->smsOptOutRepository->delete($smsOptOut);
    }

    public function check(string $rawPhone): bool
    {
        $e164 = $this->phoneNumberService->normalisePhoneNumber($rawPhone);

        return $this->smsOptOutRepository->exists($e164);
    }
}
