<?php

namespace App\Services;

use App\Enums\CommunicationRelationTypes;
use App\Enums\GlobalConfigurationKey;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Models\Text;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\TextRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class MarketingSMSService
{
    public function __construct(
        protected TextRepository $textRepository,
        protected GlobalConfigurationRepository $globalConfigurationRepository
    )
    {
    }

    public function listMarketingSMS(
        ?string $fromPhone = null,
        ?string $message = null,
    ): Builder
    {
        return $this->textRepository->list(
            direction: Text::DIRECTION_INBOUND,
            externalType: 'twilio',
            otherNumber: $fromPhone,
            message: $message,
            orderBy: Model::CREATED_AT,
            orderDirection: 'desc',
            relationType: CommunicationRelationTypes::MARKETING_CONSUMERS,
        );
    }

}
