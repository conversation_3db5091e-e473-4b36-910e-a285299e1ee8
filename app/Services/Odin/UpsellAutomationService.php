<?php

namespace App\Services\Odin;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Campaigns\CustomCampaignBudgetType;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\UpsellAutomationVariableEnum;
use App\Jobs\UpsellAutomationEndLogJob;
use App\Jobs\UpsellAutomationWorkerJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\UpsellAutomationLog;
use App\Models\UpsellAutomationLogEntry;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Campaigns\ProductBiddingService;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Batch;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;

class UpsellAutomationService
{
    const string BATCH_NAME = 'Upsell Automation';

    /**
     * @param GlobalConfigurationRepository $globalConfigurationRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ConsumerProjectProcessingService $processingService
     * @param MultiProductAssignmentStrategyContract $assignmentStrategyContract
     * @param ProductBiddingService $productBiddingService
     * @param LocationRepository $locationRepository
     * @param array $config
     */
    public function __construct(
        protected GlobalConfigurationRepository $globalConfigurationRepository,
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected ConsumerProjectProcessingService $processingService,
        protected MultiProductAssignmentStrategyContract $assignmentStrategyContract,
        protected ProductBiddingService $productBiddingService,
        protected LocationRepository $locationRepository,
        protected array $config = [],
    )
    {
        // Get config data from global configurations management upsell_automation_variables
        $globalConfigObject = $this->globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::UPSELL_AUTOMATION_VARIABLES);
        if ($globalConfigObject) {
            $this->config = $globalConfigObject->toArray()['data'] ?? [];
        } else {
            $this->config = [];
        }
    }

    /**
     * Upsell Automation entry point, this is the main processing loop for the upselling automation job / command
     * 1. Check configurations (enabled, delay time, ...)
     * 2. Initialize upselling logs
     * 3. Get leads in window
     * 4. Process each lead
     * 5. Close logs
     *
     * @param array|null $config
     * @return void
     */
    function upsellAvailableLegs(?array $config = []): void
    {
        $automationStart = now();

        // Set config from given parameters, global configurations, and defaults
        $this->updateConfig($config);
        $configCopy = $this->config; // Config copy used to fix serialization problem when passing config to post batch execution

        // Check if automation should run (minutes since last execution is greater than the delay)
        $lastExecution = $this->getLastAutomatedExecutionTime();

        // Check if we are past delay time
        if ($lastExecution) {
            $minutesSinceLastExecution = $lastExecution->diffInMinutes($automationStart);
            // Exit if the time since last execution is less than the configured delay time
            if ($minutesSinceLastExecution < $this->config[UpsellAutomationVariableEnum::AUTOMATION_DELAY_MINS->value])
                return; // Still delaying until next execution
        }

        // Check that automation is within operating time
        if (!$this->automationTimeCheck())
            return; // Automation is outside enabled time

        // Check that automation is enabled
        if (!$this->config[UpsellAutomationVariableEnum::AUTOMATION_ENABLED->value])
            return; // Automation is not enabled

        // Initialize upsell log
        $log = $this->initializeLog();
        $logId = $log->id;

        // Get range in past to search for leads
        $hoursRange = $this->config[UpsellAutomationVariableEnum::PAST_RANGE_HOURS->value];
        $timeFrameStart = now()->subHours($hoursRange);
        $timeFrameEnd = now();

        // Query returns undersold leads in the given time range
        $leads = $this->getUndersoldLeadsInWindow($timeFrameStart);

        // Get excluded companies from list and new company parameters
        $excludedCompanies = $this->getExcludedCompanies();

        // Log header information
        $logHeader = "Upsell Log: ".$this->config[UpsellAutomationVariableEnum::LOG_NAME->value]." $logId\n".
            "Time Frame Start: ".$timeFrameStart." UTC\n".
            "Time Frame End: ".$timeFrameEnd." UTC\n".
            "Undersold Leads In Time Frame: ".$leads->count()."\n".
            "Excluded Companies: ".implode(', ', $excludedCompanies)."\n".
            "Mode: ".($this->config[UpsellAutomationVariableEnum::TEST_MODE->value] ? UpsellAutomationLog::TYPE_TEST : UpsellAutomationLog::TYPE_LIVE)."\n\n";

        // Add header and store time data
        $this->addInfoLog($log->id, $logHeader);
        $this->updateLogData($log, UpsellAutomationLog::DATA_TIME_FRAME_START, $timeFrameStart);
        $this->updateLogData($log, UpsellAutomationLog::DATA_TIME_FRAME_END, $timeFrameEnd);
        $this->updateLogData($log, UpsellAutomationLog::DATA_LEADS_IN_WINDOW, $leads->count());

        // Total lead count
        $totalLeads = $leads->count();

        // If dispatching on queue, chunk and batch
        if ($this->config[UpsellAutomationVariableEnum::BATCH->value]) {
            // Chunk leads for worker jobs (lead count defined by leads_per_worker config)
            $workerChunks = $leads->chunk($this->config[UpsellAutomationVariableEnum::LEADS_PER_WORKER->value]);

            // Define batch jobs
            $batchJobs = [];
            $leadIndex = 1;
            foreach ($workerChunks as $workerChunk) {
                $batchJobs[] = new UpsellAutomationWorkerJob($workerChunk, $logId, $leadIndex, $totalLeads, $this->config, $excludedCompanies);
                $leadIndex += $this->config[UpsellAutomationVariableEnum::LEADS_PER_WORKER->value];
            }

            // Dispatch worker jobs, after completion compute log end
            $batch = Bus::batch($batchJobs)
                ->name(self::BATCH_NAME.": ".$this->config[UpsellAutomationVariableEnum::LOG_NAME->value])
                ->finally(function (Batch $batch) use ($logId, $automationStart, $configCopy) {
                UpsellAutomationEndLogJob::dispatch($logId, $configCopy, $batch->totalJobs, $batch->failedJobs);
            })
                ->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING_SINGLE)
                ->dispatch();

        } else {
            // Process leads linearly if batch is set to false
            $this->processUndersoldLeadCollection($leads, $logId, 1, $totalLeads, $excludedCompanies);
            $this->endAutomationLog($logId);
        }
        // Upselling automation complete.
    }

    /**
     * @param Collection $consumerProducts
     * @param int $logId
     * @param int $startIndex
     * @param int $totalLeads
     * @param array $excludedCompanies
     * @return void
     */
    public function processUndersoldLeadCollection(Collection $consumerProducts, int $logId, int $startIndex, int $totalLeads, array $excludedCompanies): void
    {
        // Get upsell log
        $log = UpsellAutomationLog::find($logId);
        $leadCounter = 0;

        // Loop through leads and process each
        foreach ($consumerProducts as $lead) {
            if ($this->checkKillSwitch()) {
                $this->addInfoLog($log->id, "Kill switch registered at " . now() . ", ending upsell automation.\n");
                break;
            }

            // Process individual undersold lead
            $this->processUndersoldLead($lead, $log, $startIndex + $leadCounter, $totalLeads, $excludedCompanies);
            $leadCounter++;
        }
    }

    /**
     * This is the main processing logic for each individual undersold lead
     * Here we check if the lead has available assignments, and then move to the BRS function if there are possible assignments
     * An UpsellAutomationLogEntry is created for each consumer product processed
     *
     * @param ConsumerProduct $lead
     * @param UpsellAutomationLog $log
     * @param int $currentLead
     * @param int $totalLeads
     * @param array $excludedCompanies
     * @return void
     */
    public function processUndersoldLead(ConsumerProduct $lead, UpsellAutomationLog $log, int $currentLead, int $totalLeads, array $excludedCompanies): void
    {
        // Each processed consumer product has an associated upsell automation log entry
        $cpLogData = [
            UpsellAutomationLogEntry::FIELD_LOG_ID => $log->{UpsellAutomationLog::FIELD_ID},
            UpsellAutomationLogEntry::FIELD_TYPE => UpsellAutomationLogEntry::TYPE_CONSUMER_PRODUCT,
            UpsellAutomationLogEntry::FIELD_CONSUMER_PRODUCT_ID => $lead->id,
        ];
        $cpLogData = $this->addToCpInfoLog("CP ID: ".$lead->id."   ($currentLead/$totalLeads)\n", $cpLogData);

        try {
            // Get consumer product model from ID
            $consumerProduct = ConsumerProduct::find($lead->id);

            // Get existing assignments
            $assignments = $this->productAssignmentRepository->getAllAssignmentsForConsumer($consumerProduct->consumer);

            // Put consumer data into ConsumerProject DTO used in BRS
            $consumerProject = $this->processingService->prepareConsumerProject(
                consumer: $consumerProduct->consumer,
                address: $consumerProduct->address,
                excludedCompanies: $excludedCompanies,
            );

            // Get potential campaign assignments
            $potentialCampaigns = $this->processingService->getAvailableCampaigns($consumerProject);

            // If potential campaigns for assignment are found
            if ($potentialCampaigns->isNotEmpty()) {
                // Run BRS for lead with available campaigns
                $cpLogData = $this->runBrsForUndersoldLead(
                    consumerProduct: $consumerProduct,
                    consumerProject: $consumerProject,
                    potentialCampaigns: $potentialCampaigns,
                    assignments: $assignments,
                    cpLogData: $cpLogData,
                    logId: $log->id,
                );
            } else {
                $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_NONE_AVAILABLE;
                $cpLogData = $this->addToCpInfoLog("No potential campaigns.\n\n", $cpLogData);
            }

            // Store consumer product log data
            UpsellAutomationLogEntry::query()->create($cpLogData);
        } catch (Exception $e) {
            // Enter this logic if failure along the way, store associated to cp and keep moving
            $cpLogData = $this->addToCpInfoLog("Error caught during upsell automation processing.\n".$e->getMessage()."\n".$e->getTraceAsString()."\n\n", $cpLogData);
            $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_ERROR;
            UpsellAutomationLogEntry::query()->create($cpLogData);
        }
    }

    /**
     * This is where the BRS is calculated for undersold leads to determine if a new allocation should be dispatched
     * 1. Get proposed assignments from the consumer project (where excluded companies are stored) and potential campaigns
     * 2. If proposed assignments exist, calculate new revenue from applying proposal
     * 3. If new revenue difference is greater than minimum threshold difference, dispatch allocation job
     * 4. Allocation job will recalculate BRS and perform actual deliveries (excluded companies are maintained through consumer project)
     *
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProject $consumerProject
     * @param \Illuminate\Support\Collection $potentialCampaigns
     * @param \Illuminate\Support\Collection $assignments
     * @param array $cpLogData
     * @param int $logId
     * @return array
     */
    public function runBrsForUndersoldLead(
        ConsumerProduct $consumerProduct,
        ConsumerProject $consumerProject,
        \Illuminate\Support\Collection $potentialCampaigns,
        \Illuminate\Support\Collection $assignments,
        array $cpLogData,
        int $logId,
    ): array
    {
        // Run BRS with available campaigns and current assignments
        $proposedAssignments = $this->assignmentStrategyContract->calculate(
            $consumerProject,
            $potentialCampaigns,
            $this->processingService->getPotentialProductTypes($consumerProject),
            []
        )->filter(fn(ProposedProductAssignment $proposedProductAssignment) => !$proposedProductAssignment->isExistingAssignment);

        // Proposed assignments will contain new assignments
        if ($proposedAssignments->count() > 0) {
            $salesTypeId = null; // Init to null, will be set in proposed assignments
            $cpLogData = $this->addToCpInfoLog("Proposed Assignments: \n", $cpLogData);
            $updatedCost = 0;

            // Leads are skipped if they are proposed delivery to a company over its upsold delivery limit for this execution (UpsellAutomationVariableEnum::MAX_PER_COMPANY)
            $skipLead = false;
            foreach ($proposedAssignments as $proposedAssignment) {
                $cpLogData = $this->addToCpInfoLog("    Company ID: " . $proposedAssignment->{"companyId"} .
                    " Price: $" . $proposedAssignment->{"price"} .
                    " Sales Type: " . $proposedAssignment->{"salesTypeId"} .
                    " Campaign ID: " . $proposedAssignment->{"campaignId"} .
                    "\n", $cpLogData);

                $updatedCost += $proposedAssignment->{"price"};
                $salesTypeId = $proposedAssignment->{"salesTypeId"};

                // Company limit check within the same execution
                $companyId = $proposedAssignment->{"companyId"};
                $deliveryCount = $this->getCompanyDeliveryCount($logId, $companyId);
                $deliveryLimit = $this->config[UpsellAutomationVariableEnum::MAX_PER_COMPANY->value];

                // Check if delivery count is at max, exclude company if it is
                if ($deliveryCount >= $deliveryLimit) {
                    $cpLogData = $this->addToCpInfoLog("    Company $companyId has reached upsell delivery limit ".
                        "($deliveryLimit) for this automation run. Skipping lead until next execution.\n", $cpLogData);
                    $skipLead = true;
                }
            }

            // Skip this lead if proposed assignment contains a company limited to max per execution
            if ($skipLead) {
                // This lead will be checked again next time the upselling automation executes
                $cpLogData = $this->addToCpInfoLog("\n", $cpLogData);
                $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_SKIPPED;
                return $cpLogData;
            }

            $cpLogData = $this->addToCpInfoLog("Existing Assignments: \n", $cpLogData);
            $previousCost = 0;

            // Look at existing assignments to determine BRS
            foreach ($assignments as $assignment) {
                $cpLogData = $this->addToCpInfoLog("    Company ID: " . $assignment->company_id .
                    " Cost: $" . $assignment->cost .
                    " Sales Type: " . $assignment->sale_type_id .
                    "\n", $cpLogData);
                $previousCost += $assignment->cost;

                // Get campaign associated with assignment for bid update data by sale type
                $campaign = $this->getCampaignForBudget($assignment->budget_id);

                // Get location information from consumer product
                $address = $consumerProduct->address;
                $propertyTypeId = PropertyType::RESIDENTIAL->model()->id;
                $qualityTierId = QualityTier::STANDARD->model()->id;

                $updatedBid = $this->productBiddingService->getProductBid(
                    companyCampaign: $campaign,
                    countyLocationId: $address->county_location_id,
                    stateLocationId: $address->state_location_id,
                    propertyTypeId: $propertyTypeId,
                    qualityTierId: $qualityTierId,
                    salesTypeId: $salesTypeId
                );
                $cpLogData = $this->addToCpInfoLog("        Updated bid $" . $assignment->cost . " -> $" . $updatedBid .
                    " (type " . $assignment->sale_type_id . "->" . $salesTypeId . ")\n", $cpLogData);

                // Set existing assignment to updated bid and sale type if not in test mode, will only be saved if update meets criteria
                if (!$this->config[UpsellAutomationVariableEnum::TEST_MODE->value]) {
                    // Set and do not save, will be saved if updated revenue meets criteria for upsell
                    $assignment->sale_type_id = $salesTypeId;
                    $assignment->cost = $updatedBid;
                }

                $updatedCost += $updatedBid;
            }
            // Get minimum increase needed to trigger upsell from the config variables
            $minValueThreshold = $this->config[UpsellAutomationVariableEnum::MIN_VALUE_INCREASE->value] ?? 0;
            // Calculate price difference in updated from previous
            $priceDifference = $updatedCost - $previousCost;
            // Log revenue difference
            $cpLogData = $this->addToCpInfoLog("Previous Revenue: $" . $previousCost . "\n".
                "Updated Revenue: $" . $updatedCost . "\n".
                "Difference: $" . $priceDifference . "\n", $cpLogData);
            $cpLogData[UpsellAutomationLogEntry::FIELD_OLD_REVENUE] = $previousCost;
            $cpLogData[UpsellAutomationLogEntry::FIELD_NEW_REVENUE] = $updatedCost;
            $cpLogData[UpsellAutomationLogEntry::FIELD_DIFFERENCE] = $priceDifference;

            // Trigger allocation if the price difference crosses the min value threshold
            if ($priceDifference > $minValueThreshold ?? 0) {
                $cpLogData = $this->addToCpInfoLog("$$priceDifference > $$minValueThreshold => upselling lead.\n", $cpLogData);

                // Only dispatch allocation job if not in test mode
                if (!$this->config[UpsellAutomationVariableEnum::TEST_MODE->value]) {
                    // Fire off additional legs here - BRS is recalculated, AttemptConsumerProjectAllocationJob updates sale type on existing legs sold
                    ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($consumerProduct);
                    AttemptConsumerProjectAllocationJob::dispatch($consumerProject);
                    $cpLogData = $this->addToCpInfoLog("Allocation dispatched.\n", $cpLogData);

                    // Update bid and sale type on existing product assignments. Set above when calculating bids, saved here
                    foreach ($assignments as $existingAssignment) {
                        $existingAssignment->save();
                    }
                    $cpLogData = $this->addToCpInfoLog("Existing assignments updated.\n\n", $cpLogData);

                    $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_UPSOLD;
                } else {
                    // No allocations triggered in test mode
                    $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_TEST_UPSOLD;
                    $cpLogData = $this->addToCpInfoLog("Test mode, no allocation.\n\n", $cpLogData);
                }

                // Update delivery counts, done after allocations
                $this->updateDeliveryCount($logId, $proposedAssignments->pluck("companyId")->toArray());
            } else {
                // Enter this logic if the proposed revenue is less than existing, no allocations
                $cpLogData = $this->addToCpInfoLog("$$priceDifference < $$minValueThreshold => no upsell.\n\n", $cpLogData);
                $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_REV_DECREASE;
            }
        } else {
            // Enter this logic if no new available campaigns found for lead
            $cpLogData = $this->addToCpInfoLog("No proposed assignments for cp " . $consumerProduct->id . "\n\n", $cpLogData);
            $cpLogData[UpsellAutomationLogEntry::FIELD_STATUS] = UpsellAutomationLogEntry::STATUS_NONE_AVAILABLE;
        }
        return $cpLogData;
    }

    /**
     * @param int $logId
     * @param int $companyId
     * @return int
     */
    public function getCompanyDeliveryCount(int $logId, int $companyId): int
    {
        $log = UpsellAutomationLog::find($logId);
        $deliveryLog = $log->{UpsellAutomationLog::FIELD_DATA}[UpsellAutomationLog::DATA_COMPANY_DELIVERIES] ?? [];
        return $deliveryLog[$companyId] ?? 0;
    }

    /**
     * @param int $logId
     * @param array $deliveries
     * @return void
     */
    public function updateDeliveryCount(int $logId, array $deliveries): void
    {
        // Lock the upsell log for update of delivery count
        DB::transaction(function () use ($logId, $deliveries) {
            $log = UpsellAutomationLog::where(UpsellAutomationLog::FIELD_ID, $logId)
                ->lockForUpdate()
                ->first();

            $data = $log->{UpsellAutomationLog::FIELD_DATA};
            $deliveryCount = $data[UpsellAutomationLog::DATA_COMPANY_DELIVERIES] ?? [];

            foreach ($deliveries as $deliveryCompanyId) {
                $deliveryCount[$deliveryCompanyId] = ($deliveryCount[$deliveryCompanyId] ?? 0) + 1;
            }

            $data[UpsellAutomationLog::DATA_COMPANY_DELIVERIES] = $deliveryCount;
            $log->{UpsellAutomationLog::FIELD_DATA} = $data;
            $log->update();
        });
    }

    /**
     * Returns campaign details for budget id from existing product assignment
     *
     * @param int $budgetId
     * @return CompanyCampaign|null
     */
    public function getCampaignForBudget(int $budgetId): ?CompanyCampaign
    {
        return CompanyCampaign::query()
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID)
            ->where(Budget::TABLE . '.' . Budget::FIELD_ID, $budgetId)
            ->get([DB::raw(CompanyCampaign::TABLE . '.*')])
            ->first();
    }

    /**
     * Order of preference for config values
     * 1. Given parameters in the config array will overwrite any other values
     * 2. If parameter is not given, Global Configuration Management variable will be used
     * 3. If variable is not given and not set in Global Configuration Management, default value in enum will be used
     *
     * @param array|null $config
     * @return void
     */
    function updateConfig(?array $config): void
    {
        if (!$config)
            $config = [];

        foreach (UpsellAutomationVariableEnum::cases() as $variable) {
            $metaData = $variable->getMetaData();

            // Use the manual command config if the key is defined
            if (array_key_exists($variable->value, $config)) {
                $this->config[$variable->value] = $config[$variable->value];
            }

            // Use the manual default if global config and command config are not defined
            if (!array_key_exists($variable->value, $this->config)) {
                $this->config[$variable->value] = $metaData[UpsellAutomationVariableEnum::KEY_DEFAULT];
            }

            // Cast config variable to correct type
            $this->config[$variable->value] = $variable->castToType($this->config[$variable->value]);
        }
    }

    /**
     * Returns all undersold consumer products in the time frame
     * Only consumer products that have been sold and have less product assignments than max_contact_requests
     *
     * @param Carbon $timeFrameStart
     * @return Collection
     */
    public function getUndersoldLeadsInWindow(Carbon $timeFrameStart): Collection
    {
        return ConsumerProduct::query()
            ->leftJoin(ProductAssignment::TABLE,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                '=',
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID)
            ->leftJoin(Budget::TABLE, Budget::TABLE.'.'.Budget::FIELD_ID, '=', ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_BUDGET_ID)
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::CREATED_AT, '>=', $timeFrameStart)
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->whereNotNull(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID)
            ->where(Budget::TABLE.'.'.Budget::FIELD_KEY, '!=', CustomCampaignBudgetType::EXCLUSIVE_ONLY_LEADS->value)
            ->groupBy(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID)
            ->select([
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONTACT_REQUESTS,
                DB::raw('COUNT(DISTINCT '.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID.') as pa_count'),
            ])
            ->having('pa_count', '<', DB::raw(ConsumerProduct::FIELD_CONTACT_REQUESTS))
            ->get();
    }

    /**
     * Returns the last time the upselling was ran from the automation (identified as automation by log name)
     * Automation uses log name 'Automation Log', manual commands use log name 'Manual Command Log'
     * Log name can also be changed in command parameters
     *
     * @return Carbon|null
     */
    public function getLastAutomatedExecutionTime(): ?Carbon
    {
        return UpsellAutomationLog::query()
            ->where(UpsellAutomationLog::FIELD_NAME, UpsellAutomationLog::DEFAULT_AUTOMATION_NAME)
            ->orderBy(UpsellAutomationLog::FIELD_STARTED_AT, 'DESC')
            ->get([UpsellAutomationLog::FIELD_STARTED_AT])
            ->first()?->{UpsellAutomationLog::FIELD_STARTED_AT} ?? null;
    }

    /**
     * Check that current time is between given time bounds
     *
     * @return bool
     */
    public function automationTimeCheck(): bool
    {
        $startTime = $this->config[UpsellAutomationVariableEnum::ACTIVE_START_UTC->value];
        $endTime = $this->config[UpsellAutomationVariableEnum::ACTIVE_END_UTC->value];

        $startDateTime = Carbon::today()->setTimeFromTimeString($startTime);
        $endDateTime = Carbon::today()->setTimeFromTimeString($endTime);

        if (Carbon::now()->between($startDateTime, $endDateTime))
            return true;

        return false;
    }

    /**
     * Upsell log is used to track performance and errors
     *
     * @return UpsellAutomationLog
     */
    public function initializeLog(): UpsellAutomationLog
    {
        return UpsellAutomationLog::create([
            UpsellAutomationLog::FIELD_NAME         => $this->config[UpsellAutomationVariableEnum::LOG_NAME->value],
            UpsellAutomationLog::FIELD_TYPE         => $this->config[UpsellAutomationVariableEnum::TEST_MODE->value] ? UpsellAutomationLog::TYPE_TEST : UpsellAutomationLog::TYPE_LIVE,
            UpsellAutomationLog::FIELD_STARTED_AT   => now(),
            UpsellAutomationLog::FIELD_LEADS_UPSOLD => 0,
            UpsellAutomationLog::FIELD_ADDITIONAL_REVENUE => 0,
            UpsellAutomationLog::FIELD_DATA         => [UpsellAutomationLog::DATA_CONFIG => $this->config],
        ]);
    }

    /**
     * @param UpsellAutomationLog $log
     * @param string $key
     * @param mixed $value
     * @return UpsellAutomationLog
     */
    public function updateLogData(UpsellAutomationLog $log, string $key, mixed $value): UpsellAutomationLog
    {
        $data = $log->{UpsellAutomationLog::FIELD_DATA};
        $data[$key] = $value;
        $log->update([
            UpsellAutomationLog::FIELD_DATA => $data,
        ]);
        return $log;
    }

    /**
     * @param string $line
     * @param array $cpInfoLog
     * @return array
     */
    public function addToCpInfoLog(string $line, array $cpInfoLog): array
    {
        logger()->info($line);
        if ($this->config[UpsellAutomationVariableEnum::PRINT_LOG->value])
            print $line;

        if (!array_key_exists(UpsellAutomationLogEntry::FIELD_ENTRY, $cpInfoLog))
            $cpInfoLog[UpsellAutomationLogEntry::FIELD_ENTRY] = "";

        $cpInfoLog[UpsellAutomationLogEntry::FIELD_ENTRY] .= $line;
        return $cpInfoLog;
    }

    /**
     * @param int $logId
     * @param string $info
     * @param array|null $data
     * @return UpsellAutomationLogEntry
     */
    public function addInfoLog(int $logId, string $info, ?array $data = null): UpsellAutomationLogEntry
    {
        logger()->info($info);
        if ($this->config[UpsellAutomationVariableEnum::PRINT_LOG->value])
            print $info;

        return UpsellAutomationLogEntry::query()->create([
            UpsellAutomationLogEntry::FIELD_LOG_ID => $logId,
            UpsellAutomationLogEntry::FIELD_TYPE => UpsellAutomationLogEntry::TYPE_INFO,
            UpsellAutomationLogEntry::FIELD_ENTRY => $info,
            UpsellAutomationLogEntry::FIELD_DATA => $data,
        ]);
    }

    /**
     * Checks the kill_switch Global Configurations management key, if true automation will exit on next consumer product
     * @return bool
     */
    public function checkKillSwitch(): bool
    {
        $globalConfigObject = $this->globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::UPSELL_AUTOMATION_VARIABLES);
        if ($globalConfigObject) {
            $configData = $globalConfigObject->toArray()['data'] ?? [];
            $killSwitchValue = $configData[UpsellAutomationVariableEnum::KILL_SWITCH->value] ?? 'false';
            return filter_var($killSwitchValue, FILTER_VALIDATE_BOOLEAN);
        }
        return false;
    }

    /**
     * Used by worker job to ensure the same config as parent
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        $this->config = $config;
    }

    /**
     * @param int $logId
     * @param int $totalWorkerJobs
     * @param int $failedWorkerJobs
     * @return void
     */
    public function endAutomationLog(int $logId, int $totalWorkerJobs = 0, int $failedWorkerJobs = 0): void
    {
        // Get latest log data
        $log = UpsellAutomationLog::find($logId);
        $automationStart = $log->{UpsellAutomationLog::FIELD_STARTED_AT};

        $executionMetrics = UpsellAutomationLogEntry::query()
            ->where(UpsellAutomationLogEntry::FIELD_LOG_ID, $logId)
            ->whereIn(UpsellAutomationLogEntry::FIELD_STATUS, [UpsellAutomationLogEntry::STATUS_UPSOLD, UpsellAutomationLogEntry::STATUS_TEST_UPSOLD])
            ->groupBy(UpsellAutomationLogEntry::FIELD_LOG_ID)
            ->get([
                DB::raw('SUM('.UpsellAutomationLogEntry::TABLE.'.'.UpsellAutomationLogEntry::FIELD_DIFFERENCE.') as '.UpsellAutomationLog::FIELD_ADDITIONAL_REVENUE),
                DB::raw('COUNT(DISTINCT '.UpsellAutomationLogEntry::TABLE.'.'.UpsellAutomationLogEntry::FIELD_ID.') as '.UpsellAutomationLog::FIELD_LEADS_UPSOLD),
            ])->first();

        // Update log with final numbers
        $log->{UpsellAutomationLog::FIELD_LEADS_UPSOLD} = $executionMetrics->{UpsellAutomationLog::FIELD_LEADS_UPSOLD} ?? 0;
        $log->{UpsellAutomationLog::FIELD_ADDITIONAL_REVENUE} = $executionMetrics->{UpsellAutomationLog::FIELD_ADDITIONAL_REVENUE} ?? 0;
        $log->{UpsellAutomationLog::FIELD_ENDED_AT} = now();
        $log->save();

        // Information stored in footer
        $automationEnd = now();
        $diffInSeconds = $automationStart->diffInSeconds($automationEnd);
        $this->updateLogData($log, UpsellAutomationLog::DATA_AUTOMATION_SEC, $diffInSeconds);
        $this->updateLogData($log, UpsellAutomationLog::DATA_TOTAL_WORKER_JOBS, $totalWorkerJobs);
        $this->updateLogData($log, UpsellAutomationLog::DATA_FAILED_WORKER_JOBS, $failedWorkerJobs);

        $logFooter = "Total Leads Upsold: " . $log->{UpsellAutomationLog::FIELD_LEADS_UPSOLD} . "\n".
            "Total Additional Revenue: $" . $log->{UpsellAutomationLog::FIELD_ADDITIONAL_REVENUE} . "\n".
            "Execution Time: " . $diffInSeconds . " seconds\n";
        $this->addInfoLog($log->id, $logFooter);
    }

    /**
     * Returns companies that are either on the exclude list, are a new activation within window, or are a reactivation within window
     * @return array
     */
    public function getExcludedCompanies(): array
    {
        $newAndReactCompanies = Company::query()
            ->join(ProductAssignment::TABLE, fn ($join) =>
                $join->on(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '>=', now()->subDays($this->config[UpsellAutomationVariableEnum::REACT_DAYS_COUNT->value]))
                    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::FIELD_DELIVERED, true)
            )
            ->select([
                Company::TABLE.'.'.Company::FIELD_ID,
                DB::raw('MIN('.ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT.') as min_created_at'),
            ])
            ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            ->whereNotIn(Company::TABLE.'.'.Company::FIELD_ID, $this->config[UpsellAutomationVariableEnum::NEW_COMPANY_INCLUDE_IDS->value])
            ->groupBy(Company::TABLE.'.'.Company::FIELD_ID)
            ->having('min_created_at', '>', now()->subHours($this->config[UpsellAutomationVariableEnum::NEW_COMPANY_DELAY_HRS->value]))
            ->get()
            ->pluck(Company::FIELD_ID)
            ->toArray();

        return array_values(array_unique(array_merge($newAndReactCompanies, $this->config[UpsellAutomationVariableEnum::EXCLUDED_COMPANY_IDS->value])));
    }
}
