import axios from 'axios';

export default class SalesOverviewApi {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new SalesOverviewApi('internal-api','sales-overview', 'v1');
    }

    getSalesOverview(filter) {
        return this.axios().get('/', {
            params: filter
        })
    }

    getCallsForUser(userId, direction, filters) {
        return this.axios().get(`/users/${userId}/calls`, {
            params: {...filters, direction: direction}
        });
    }

    getTextsForUser(userId, direction, filters) {
        return this.axios().get(`/users/${userId}/texts`, {
            params: {...filters, direction: direction}
        });
    }

    getEmailsForUser(userId, direction, filters) {
        return this.axios().get(`/users/${userId}/emails`, {
            params: {...filters, direction: direction}
        });
    }

    getUserDemos(userId, filters) {
        return this.axios().get(`/users/${userId}/demos`, {
            params: filters
        });
    }

    searchDemos(searchPayload) {
        return this.axios().post('/demos/filters', searchPayload);
    }

    exportDemos(searchPayload) {
        return this.axios().post('/demos/export-to-csv', searchPayload);
    }

    getDemoFilterOptions() {
        return this.axios().get('/demos/filters');
    }

    searchUsers(searchText) {
        return this.axios().get('/demos/users', {
            params: { search: searchText }
        });
    }

    associateCompanyWithDemo(demoId, companyId) {
        return this.axios().patch('/demos/associate-company', {
            company_id: companyId,
            demo_id: demoId,
        });
    }

    getUserCheckIns(userId, filters) {
        return this.axios().get(`/users/${userId}/check-ins`, {
            params: filters
        });
    }

    searchCheckIns(searchPayload) {
        return this.axios().post('/check-ins/filters', searchPayload);
    }

    getCheckInFilterOptions() {
        return this.axios().get('/check-ins/filters');
    }

    associateCompanyWithCheckIn(demoId, companyId) {
        return this.axios().patch('/check-ins/associate-company', {
            company_id: companyId,
            demo_id: demoId,
        });
    }
}
