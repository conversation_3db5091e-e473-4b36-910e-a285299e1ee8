<template>
    <Modal :restrict-width="false" :hide-confirm="true" :dark-mode="darkMode" :full-width="false"
           @close="$emit('close-modal')">
        <template v-slot:header>
            {{user.name}}'s Check Ins
        </template>
        <template v-slot:content>
            <div v-if="!loading" class="grid grid-cols-3 gap-2">
                <demo-card v-for="demo in checkIns" :dark-mode="darkMode" :event="demo.event"/>
            </div>
            <loading-spinner v-else></loading-spinner>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import {capitalize} from "../../../../composables/stringHelper.js";
import SalesOverviewApi from "../services/api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import SimpleCard from "../../MarketingCampaign/SimpleCard.vue";
import DisplayDateRange from "../../Shared/components/DisplayDateRange.vue";
import simpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useTimeHelper from "../../../../composables/useTime.js";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import Badge from "../../Shared/components/Badge.vue";
import DemoCard from "../../Demo/DemoCard.vue";

export default {
    name: "CheckIns",
    computed: {
        simpleIcon() {
            return simpleIcon
        }
    },
    components: {
        DemoCard,
        Badge,
        LabeledValue,
        DisplayDateRange,
        SimpleCard,
        SimpleIcon,
        LoadingSpinner,
        Modal
    },
    emits: ['close-modal'],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filters: {
            type: Object,
            required: true
        },
        userId: {
            type: Number,
            required: true
        },
        api: {
            type: SalesOverviewApi,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            checkIns: [],
            user: {},
            headers: [
                {title: 'Host', field: 'host'},
                {title: 'Demo Status', field: 'status'},
                {title: 'Title', field: 'event_title'},
                {title: 'Start Time', field: 'event_start_time'},
                {title: 'End Time', field: 'event_end_time'},
                {title: 'Participants', field: 'event_participants', cols: 2},
            ],
            paginationData: {},
            requestFilters: {},
        }
    },
    mounted() {
        this.requestFilters = {...this.filters};
        this.requestFilters['per_page'] = 25;

        this.getDemos();
    },
    methods: {
        capitalize,
        ...useTimeHelper(),
        async getDemos() {
            this.loading = true;
            try {
                const response = await this.api.getUserCheckIns(this.userId, this.requestFilters)
                this.checkIns = response.data.data.check_ins.data;
                this.user = response.data.data.user;
                this.paginationData = response.data.data.check_ins.meta;
            } catch (err) {
                console.error(err)
            }
            this.loading = false;
        },
    },
}
</script>
