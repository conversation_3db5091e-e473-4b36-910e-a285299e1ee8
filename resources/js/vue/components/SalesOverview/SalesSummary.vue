<template>
    <div>
        <div class="w-full">
            <div class="w-full flex-auto relative">
                <simple-table
                    v-model="tableFilter"
                    :table-filters="tableFilters"
                    :dark-mode="darkMode"
                    :data="salesOverviewData?.data ?? []"
                    :pagination-data="salesOverviewData?.meta ?? {}"
                    :headers="headers"
                    :loading="loading"
                    @search="handleSearch"
                    @page-change="() => handleSearch()"
                    @per-page-change="() => handleSearch()"
                    @reset="handleTableReset"
                    @column-clicked="handleColumnClick"
                    :current-per-page="salesOverviewData?.meta?.current_page"
                    row-classes="gap-5 grid items-center py-2 rounded px-5"
                    class="border-none"
                >
                    <template #visible-filters>
                        <labeled-value label="Date Range (MT)">
                            <date-picker
                                v-model="tableFilter.date_range"
                                range
                                :enable-time-picker="false"
                                :dark="darkMode"
                                auto-apply
                                :clearable="false"
                                placeholder="mm-dd-yy"
                                format="PP"
                                timezone="America/Denver"
                            >
                                <template #input-icon>
                                    <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                            fill="#0081FF"/>
                                        <path
                                            d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                            fill="#0081FF"/>
                                    </svg>
                                </template>
                            </date-picker>
                        </labeled-value>
                        <labeled-value>
                            <user-search-autocomplete
                                v-model="tableFilter.user_id"
                                :dark-mode="darkMode"
                                placeholder="User"
                            >
                            </user-search-autocomplete>
                        </labeled-value>
                    </template>
                </simple-table>
            </div>
        </div>
        <Calls :direction="direction" v-if="activeModal === 'calls'" :dark-mode="darkMode" @close-modal="closeModal" :user-id="userId" :api="api" :filters="tableFilter"/>
        <Texts :direction="direction" v-if="activeModal === 'texts'" :dark-mode="darkMode" @close-modal="closeModal" :user-id="userId" :api="api" :filters="tableFilter"/>
        <Emails :direction="direction" v-if="activeModal === 'emails'" :dark-mode="darkMode" @close-modal="closeModal" :user-id="userId" :api="api" :filters="tableFilter"/>
        <Demos  v-if="activeModal === 'demos'" :dark-mode="darkMode" @close-modal="closeModal" :user-id="userId" :api="api" :filters="tableFilter"/>
        <CheckIns  v-if="activeModal === 'check_ins'" :dark-mode="darkMode" @close-modal="closeModal" :user-id="userId" :api="api" :filters="tableFilter"/>
    </div>
</template>
<script>
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import UserSearchAutocomplete from "../Shared/components/User/UserSearchAutocomplete.vue";
import DatePicker from "@vuepic/vue-datepicker";
import dayjs from "dayjs";
import Badge from "../Shared/components/Badge.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import Calls from "./modals/Calls.vue";
import Texts from "./modals/Texts.vue";
import Emails from "./modals/Emails.vue";
import Demos from "./modals/Demos.vue";
import {useUserStore} from "../../../stores/user-store.js";
import SalesOverviewApi from "./services/api.js";
import CheckIns from "./modals/CheckIns.vue";

const defaultFilter = {
    date_range: [
        dayjs().subtract(1, 'week').toDate(),
        dayjs().toDate(),
    ],
    sort_by: [],
    per_page: 100
}

export default {
    name: "SalesOverview",
    components: {
        CheckIns,
        Demos, Emails, Texts, Calls, LabeledValue, Badge, DatePicker, UserSearchAutocomplete, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        api: {
            type: SalesOverviewApi,
            required: true,
        },
    },
    methods: {
        resetTableFilter() {
            this.tableFilter = {
                ...defaultFilter
            }
        },
        handleTableReset() {
            this.resetTableFilter()
            this.handleSearch()
        },
        async handleSearch() {
            this.loading = true
            if (!this.tableFilter.date_range) {
                this.resetTableFilter()
            }
            try {
                const response = await this.api.getSalesOverview(this.tableFilter)
                this.salesOverviewData = response.data
            } catch (err) {
                console.error(err)
            }
            this.loading = false
        },
        handleColumnClick(key, item) {
            this.userId = item.user_id;

            switch (key) {
                case 'calls_in':
                    this.direction = 'inbound';
                    this.activeModal = 'calls';
                    break
                case 'calls_out':
                    this.direction = 'outbound';
                    this.activeModal = 'calls';
                    break
                case 'texts_in':
                    this.direction = 'inbound';
                    this.activeModal = 'texts';
                    break
                case 'texts_out':
                    this.direction = 'outbound';
                    this.activeModal = 'texts';
                    break
                case 'emails_in':
                    this.direction = 'inbound';
                    this.activeModal = 'emails';
                    break
                case 'emails_out':
                    this.direction = 'outbound';
                    this.activeModal = 'emails';
                    break
                case 'completed_demos':
                    this.activeModal = 'demos';
                    break
                case 'check_ins':
                    this.activeModal = 'check_ins';
                    break
            }
        },
        closeModal() {
            this.direction = null;
            this.activeModal = null;
            this.userId = 0;
        }
    },
    async mounted() {
        this.resetTableFilter()
        this.handleSearch()
    },
    data() {
        return {
            loading: false,
            salesOverviewData: {},
            tableFilter: {...defaultFilter},
            tableFilters: [],
            headers: [
                {title: 'Name', field: 'user_name', sortable: true},
                {title: 'Calls Total', field: 'calls_total', sortable: true},
                {title: 'Calls In', field: 'calls_in', sortable: true, clickable: true},
                {title: 'Calls Out', field: 'calls_out', sortable: true, clickable: true},
                {title: 'Texts Total', field: 'texts_total', sortable: true},
                {title: 'Texts In', field: 'texts_in', sortable: true, clickable: true},
                {title: 'Texts Out', field: 'texts_out', sortable: true, clickable: true},
                {title: 'Emails Total', field: 'emails_total', sortable: true},
                {title: 'Emails In', field: 'emails_in', sortable: true, clickable: false},
                {title: 'Emails Out', field: 'emails_out', sortable: true, clickable: false},
                {title: 'Demos', field: 'completed_demos', sortable: true, clickable: true},
                {title: 'Check ins', field: 'check_ins', sortable: true, clickable: true},
                {title: 'Communications Total', field: 'total', sortable: true},
            ],
            direction: null,
            activeModal: null,
            userId: 0,
            userStore: useUserStore()
        }
    },
    computed: {
        emailClickable() {
            return this.userStore.user && this.userStore.user.impersonating === false;
        }
    },
    watch: {
        emailClickable() {
            this.headers.find(header => header.field === 'emails_in').clickable = this.emailClickable;
            this.headers.find(header => header.field === 'emails_out').clickable = this.emailClickable;
        }
    }
}
</script>
