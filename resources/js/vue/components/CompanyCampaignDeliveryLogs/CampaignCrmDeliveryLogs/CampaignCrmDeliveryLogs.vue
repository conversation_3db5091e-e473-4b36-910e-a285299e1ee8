<template>
    <div>
        <simple-table
            :dark-mode="darkMode"
            title="Company Campaign CRM Delivery Logs"
            v-model="tableFilter"
            :table-filters="tableFilters"
            @update:modelValue="getDeliveryLogs"
            :pagination-data="paginationData"
            :data="data"
            :headers="headers"
            :loading="loading"
            @search="handleSearch"
            @reset="handleReset"
            :current-per-page="currentPerPage"
        >
            <template v-slot:visible-filters>
                <company-search-autocomplete
                    v-model="tableFilter.company_id"
                    :dark-mode="darkMode"
                />
            </template>
            <template v-slot:row.col.date="{item,value}">
                <div>{{value}}</div>
            </template>
            <template v-slot:row.col.company="{item, value}">
                <a :href="`/companies/${value?.id}`" target="_blank" class="flex text-primary-500 cursor-pointer" v-if="value">
                    {{ value?.name }} ({{ value?.id }})
                </a>
                <p v-else>N/A</p>
            </template>
            <template v-slot:row.col.campaign="{item, value}">
                <div class="flex gap-1 items-center" v-if="value">
                    <div>
                        {{ value?.name }} ({{ value?.id }})
                    </div>
                    <div class="flex flex-col">
                        <badge :dark-mode="darkMode">{{ value?.status }}</badge>
                    </div>
                </div>
                <p v-else>N/A</p>
            </template>
            <template v-slot:row.col.consumer_product="{item, value}">
                <a :href="`/consumer-product?consumer_product_id=${value?.id}`"
                   target="_blank"
                   class="flex items-center gap-1 cursor-pointer text-primary-500"
                v-if="value">
                    <div>
                        {{ value?.name }} ({{ value?.id }})
                    </div>
                    <div class="flex flex-col">
                        <badge :dark-mode="darkMode">{{ value?.status }}</badge>
                    </div>
                </a>
                <p v-else>N/A</p>
            </template>
            <template v-slot:row.col.crm_display_name="{item,value}">
                <div>{{value}}</div>
            </template>
            <template v-slot:row.col.success="{item,value}">
                <badge
                    :dark-mode="darkMode"
                    :color="value === 'Success' ? 'green' : 'red'"
                >
                    {{ value }}
                </badge>
            </template>
            <template v-slot:row.col.payload="{item,value}">
                <div
                    @click="showPayload = formatPayload(item)"
                    class="flex gap-1 text-primary-500 cursor-pointer"
                >
                    <div>View</div>
                    <simple-icon
                        :dark-mode="darkMode"
                        :color="simpleIcon.colors.BLUE"
                        :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"
                    />
                </div>
            </template>

            <template v-slot:custom-buttons>
                <custom-button @click="exportToCsv" :color="'primary-outline'" :disabled="loading">Export to CSV</custom-button>
            </template>

        </simple-table>
        <modal
            v-if="showPayload !== null"
            @close="showPayload = null"
            :dark-mode="darkMode"
            small
            hide-confirm
        >
            <template v-slot:header>View Delivery Logs Payload</template>
            <template v-slot:content>
                <div class="rounded-md p-2" :class="{
                    'bg-green-100' : this.showPayload.success === 'Success',
                    'bg-red-100' : this.showPayload.success === 'Failure',
                    }">
                    <div v-if="formattedPayload" :class="{'text-slate-500': !darkMode, 'text-blue-500': darkMode}">
                        <div v-if="formattedPayload.body">
                            <p class="font-semibold mt-2"><strong>Body:</strong></p>
                            <div class="mt-2" v-html="formattedPayload.body"></div>
                        </div>
                        <div v-if="formattedPayload.error">
                            <p class="font-semibold mt-2"><strong>Error:</strong></p>
                            <pre class="whitespace-pre-wrap text-sm text-red-500 mt-2">{{ formattedPayload.error }}</pre>
                        </div>
                    </div>
                    <pre v-else class="whitespace-pre-wrap text-sm">{{ showPayload.payload }}</pre>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import ApiService from "./services/api.js";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import Badge from "../../Shared/components/Badge.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";
import Modal from "../../Shared/components/Modal.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import {downloadCsvString} from "../../../../composables/exportToCsv.js";

const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 10,
}
const simpleIcon = useSimpleIcon()
export default {
    name: "CampaignCrmDeliveryLogs",
    components: {CustomButton, Modal, CompanySearchAutocomplete, SimpleIcon, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: ApiService.make(),
            simpleIcon,
            loading: false,
            tableFilter: {},
            headers: [
                {title: "Date", field: 'date'},
                {title: "Company", field: 'company', cols: 2},
                {title: "Campaign", field: 'campaign', cols: 2},
                {title: "Product", field: 'consumer_product', cols: 2},
                {title: "CRM Integration", field: 'crm_display_name', cols: 2},
                {title: "Status", field: 'success'},
                {title: "Details", field: 'payload'},
            ],
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'campaign',
                    title: 'Enter Campaign Name or ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'consumer_product',
                    title: 'Enter Consumer Product ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoice_id',
                    title: 'Enter Invoice ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'delivery_status',
                    title: 'Delivery Status',
                    options: [
                        {name: 'Successful', id: '1'},
                        {name: 'Failed', id: '0'},
                    ]
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    field: 'date',
                    title: 'Date'
                }
            ],
            data: [],
            paginationData: {},
            showPayload: null,
            formattedPayload: null,
            currentPerPage: DEFAULT_TABLE_FILTER.perPage,
        }
    },
    created() {
        this.tableFilter = {...DEFAULT_TABLE_FILTER}
        this.getDeliveryLogs();
    },
    methods: {
        async getDeliveryLogs() {
            this.loading = true;

            let date = this.tableFilter?.date ?? null;

            const sentData = {
                ...this.tableFilter,
                "date[from]": date?.from,
                "date[to]": date?.to,
            };

            const response = await this.api.getDeliveryLogs(sentData)
            const {data, links, meta} = response.data
            this.data = data;
            this.paginationData = {links, ...meta}
            this.loading = false;
        },
        async handleSearch() {
            this.tableFilter = {...this.tableFilter, ...DEFAULT_TABLE_FILTER}
            await this.getDeliveryLogs();
        },
        async handleReset() {
            this.tableFilter = {...DEFAULT_TABLE_FILTER}
            await this.getDeliveryLogs();
        },
        formatPayload(item) {
            try {
                let payload = typeof item.payload === 'string' ? JSON.parse(item.payload) : item.payload;

                if (typeof payload.body === 'string') {
                    // Check if the body is a JSON string that can be parsed
                    try {
                        payload.body = JSON.parse(payload.body);
                    } catch (e) {
                        // If not, it remains a string
                    }
                }
                // Format the body content
                let formattedBody = this.formatBody(payload.body);

                this.formattedPayload = {
                    ...item,
                    body: formattedBody,
                    error: payload.error || null,
                };

                return this.formattedPayload;
            } catch (error) {
                console.error("Error parsing payload:", error);
                return {
                    ...item,
                    payload: "Invalid JSON format",
                };
            }
        },
        formatBody(body) {
            if (typeof body === 'object') {
                // Convert object to pretty JSON
                return `<pre class="whitespace-pre-wrap text-sm">${JSON.stringify(body, null, 4)}</pre>`;
            } else if (Array.isArray(body)) {
                // Convert array to pretty JSON
                return `<pre class="whitespace-pre-wrap text-sm">${JSON.stringify(body, null, 4)}</pre>`;
            } else if (typeof body === 'string') {
                // Check if it's an HTML string
                if (body.trim().startsWith('<')) {
                    return body;
                } else {
                    // Otherwise, treat it as plain text
                    return `<pre class="whitespace-pre-wrap text-sm">${body}</pre>`;
                }
            } else {
                // Handle other types (e.g., numbers, booleans)
                return `<pre class="whitespace-pre-wrap text-sm">${String(body)}</pre>`;
            }
        },
        exportToCsv() {
            this.loading = true;
            this.api.exportDeliveryLog({
                ...this.tableFilter,
                "date[from]": this.tableFilter?.date?.from,
                "date[to]": this.tableFilter?.date?.to,
            }).then(resp => {
                downloadCsvString(
                    [
                        'Date',
                        'Company Name',
                        'Company ID',
                        'Campaign Name',
                        'Lead ID',
                        'Lead Name',
                        'CRM',
                        'Status',
                        'Payload',
                    ],
                    resp.data.data.map(log => [
                        log.date,
                        log.company?.name,
                        log.company?.id,
                        log.campaign?.name,
                        log.consumer_product?.id,
                        log.consumer_product?.name,
                        log.crm_display_name,
                        log.success,
                        JSON.stringify(log.payload).replace(/"/g, '""').replace(/,/g, '\\,'),
                    ]),
                    `crm_delivery_log_${Math.floor(Date.now() / 1000)}`
                )
            })
                .catch(e => console.error(e))
                .finally(() => this.loading = false);
        }
    }
}
</script>

<style scoped>

</style>
