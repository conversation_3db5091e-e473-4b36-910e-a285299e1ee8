import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new Api('internal-api','billing', 'v1');
    }

    getCreditTypes(params = {}) {
        return this.axios().get(`/credits/types`, {
            params
        })
    }

    getCreditEvents(creditId) {
        return this.axios().get(`/credits/events/${creditId}`)
    }
    createCreditType(newCreditType) {
        return this.axios().post(`/credits/`, newCreditType)
    }

    updateCreditTypes(creditTypes) {
        return this.axios().put(`/credits/`, {
            credit_types: creditTypes
        })
    }

}
