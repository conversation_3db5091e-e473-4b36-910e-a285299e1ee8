<template>
    <Modal
        :loading-confirmation="loadingConfirmation"
        :dark-mode="darkMode"
        @confirm="handleSaveProfile"
        @close="$emit('close')"
        :hide-cancel="readonly"
        :hide-confirm="readonly"
    >
        <template v-slot:header>
            <div>
                Manage Billing Profile {{data?.id}}
            </div>
        </template>
        <template v-slot:content>
            <simple-alert
                v-if="errorHandler.message !== null"
                :variant="SIMPLE_ALERT_VARIANTS.LIGHT_RED"
                :dark-mode="darkMode"
                :content="errorHandler.message"
            >
            </simple-alert>
            <simple-alert
                v-if="data.default"
                :variant="SIMPLE_ALERT_VARIANTS.LIGHT_BLUE"
                :dark-mode="darkMode"
                content="Default billing profiles are set to all campaigns"
            >
            </simple-alert>
            <billing-profile-form
                v-model="data"
                v-if="!loading"
                :company-id="companyId"
                :dark-mode="darkMode"
                :billing-profile-id="billingProfileId"
                :readonly="readonly"
            />
            <loading-spinner v-else/>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "../../services/billing-profiles-api";
import useErrorHandler from "../../../../../composables/useErrorHandler.js";
import SimpleAlert, {SIMPLE_ALERT_VARIANTS} from "../../../Shared/components/SimpleAlert.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";
import {ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import BillingProfileForm from "./BillingProfileForm.vue";

export default {
    name: "EditBillingProfileModal",
    components: {
        LoadingSpinner,
        BillingProfileForm,
        SimpleAlert,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Object,
            default: {}
        },
        billingProfileId: {
            type: Number,
            required: false
        },
        companyId: {
            type: Number,
            required: false
        }
    },
    emits: ['close', 'confirm'],
    data() {
        return {
            SIMPLE_ALERT_VARIANTS,
            loadingConfirmation: false,
            errorHandler: useErrorHandler(),
            apiService: ApiService.make(),
            loading: false,
            toastNotificationStore: useToastNotificationStore(),
            rolesPermissions: useRolesPermissions(),
        }
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        editing() {
            return this.data?.id || this.billingProfileId
        },
    },
    methods: {
        async handleSaveProfile() {
            this.loadingConfirmation = true;

            try {
                if (this.editing) {
                    await this.apiService.updateBillingProfile(this.data.id, this.data)
                } else {
                    await this.apiService.createBillingProfile(this.data)
                }

                const isFinanceOwner = this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)

                const message = isFinanceOwner ? 'Saved successfully' : 'Action requested';

                this.toastNotificationStore.notifySuccess({
                    message
                })

                this.$emit('confirm')
            } catch (err) {
                this.errorHandler.handleError(err, 'Validation Error')
                this.toastNotificationStore.notifyError({
                    message: this.errorHandler.message
                })
            }

            this.loadingConfirmation = false;
        },
    }
}
</script>
