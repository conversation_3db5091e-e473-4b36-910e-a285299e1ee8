<template>
    <Modal
        :dark-mode="darkMode"
        no-buttons
    >
        <template v-slot:header>
            <div>
                Credit Events - {{creditId}}
            </div>
        </template>
        <template v-slot:content>
            <loading-spinner v-if="loading"/>
            <div v-else class="flex flex-col">
                <custom-button
                    @click="exportEvents"
                    :disabled="exporting"
                    class="self-end"
                >
                    Export
                </custom-button>
                <event-log  :dark-mode="darkMode" :data="events">
                    <template #event-icon="{event}">
                        <simple-icon
                            v-if="getEventIconSlug(event)"
                            :icon="getEventIconSlug(event)"
                            :color="getEventIconColor(event)"
                        />
                    </template>
                    <template #title="{event}">
                        <div v-if="event.event === 'added_to_company'">
                            <labeled-value label="Credit added to company">
                            <span class="flex gap-1">
                                {{ event.amount }} of {{ event.credit_type_name }} added to
                                <entity-hyperlink
                                    type="company"
                                    :entity-id="event.company_id"
                                    :dark-mode="darkMode"
                                    :suffix="event.company_name"
                                >
                                </entity-hyperlink>
                            </span>
                                <p class="text-xs">Expiry: {{event.formatted_expiry_date}}</p>
                                <p v-if="event.credit_notes" class="text-xs">Notes: {{event.credit_notes}}</p>
                            </labeled-value>
                        </div>
                        <div v-if="event.event === 'applied_to_invoice'">
                            <labeled-value label="Credit applied to invoice">
                            <span class="flex gap-1">
                                {{ event.amount }} applied to invoice
                                <entity-hyperlink
                                    type="invoice"
                                    :entity-id="event.invoice_id"
                                    :suffix="event.invoice_id"
                                    :dark-mode="darkMode"
                                >
                                </entity-hyperlink>
                            </span>
                            </labeled-value>
                        </div>
                        <div v-if="event.event === 'expired'">
                            <labeled-value label="Credit expired">
                                {{ event.amount }} expired
                            </labeled-value>
                        </div>
                        <div v-if="event.event === 'balance'">
                            <labeled-value label="Credit Balance">
                                {{event.amount}} outstanding
                            </labeled-value>
                        </div>
                    </template>
                </event-log>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import BillingProfileForm from "./BillingProfileForm.vue";
import EventLog from "../../../Companies/components/Territory/components/shared/EventLog.vue";
import Api from "../../services/credit-api.js";
import EntityHyperlink from "../EntityHyperlink.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import {downloadCsvString} from "../../../../../composables/exportToCsv.js";

export default {
    name: "CreditLifeCycleModal",
    components: {
        CustomButton,
        SimpleIcon,
        LabeledValue,
        EntityHyperlink,
        EventLog,
        LoadingSpinner,
        BillingProfileForm,
        SimpleAlert,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        creditId: {
            type: Number,
            required: false
        }
    },
    data() {
        return {
            events: [],
            loading: false,
            exporting: false
        }
    },
    computed: {
        api() {
            return Api.make()
        },
        simpleIcon(){
            return useSimpleIcon()
        }
    },
    mounted() {
        this.getCreditLifeCycle()
    },
    methods: {
        exportEvents(){
            this.exporting = true;

            const formattedItems = this.events.map(item => [
                item.company_id,
                item.company_name,
                item.event,
                item.amount,
                item.invoice_id,
                item.credit_notes,
                item.date,
            ])

            const headers = [
                'Company Id',
                'Company Name',
                'Event',
                'Amount',
                'Invoice Id',
                'Notes',
                'Date',
            ];

            downloadCsvString(
                headers,
                [
                    ...formattedItems,
                    Array.from({length: headers.length}, () => ''),
                ],
                `credit_events_${Math.floor(Date.now() / 1000)}`
            )

            this.exporting = false;
        },
        async getCreditLifeCycle() {
            this.loading = true

            const response = await this.api.getCreditEvents(this.creditId)

            this.events = response.data.data

            this.loading = false
        },

        formatDate(date) {
            const d = new Date(date);
            return d.toLocaleDateString();
        },
        getEventIconSlug(event){
            return {
                added_to_company: this.simpleIcon.icons.PLUS_CIRCLE,
                applied_to_invoice: this.simpleIcon.icons.CHECK_CIRCLE,
                expired: this.simpleIcon.icons.X_MARK,
                balance: this.simpleIcon.icons.CIRCLE,
            }[event.event]
        },
        getEventIconColor(event){
            return {
                added_to_company: this.simpleIcon.colors.GREEN,
                applied_to_invoice: this.simpleIcon.colors.BLUE,
                expired: this.simpleIcon.colors.RED,
                balance: this.simpleIcon.colors.YELLOW,
            }[event.event]
        },
    }
}
</script>
