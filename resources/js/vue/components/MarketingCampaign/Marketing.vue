<template>
    <tabbed-page
        title="Marketing"
        :dark-mode="darkMode"
        :tabs="tabs"
        tab-classes="px-10"
    />
</template>
<script>
import MarketingCampaignConsumersTable from "./MarketingCampaignConsumersTable.vue";
import MarketingLogsTable from "./MarketingLogsTable.vue";
import {markRaw} from "vue";
import TabbedPage from "../Shared/components/TabbedPage.vue";
import EmailMarketingCampaignsTab from "./EmailMarketingCampaignsTab.vue";
import DripEmailMarketingCampaignsTab from "./DripEmailMarketingCampaignsTab.vue";
import ManagementTab from "./ManagementTab.vue";
import SMSMarketingCampaignsTab from "./SMSMarketingCampaignsTab.vue";
import SmsManagement from "./SMSManagement.vue";

export default {
    name: "Marketing",
    components: {TabbedPage, MarketingLogsTable, MarketingCampaignConsumersTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            tabs: [
                { name: 'Email Marketing Campaigns', current: true, component: markRaw(EmailMarketingCampaignsTab)},
                { name: 'SMS Marketing Campaigns', current: false, component: markRaw(SMSMarketingCampaignsTab)},
                { name: "Drip Marketing Campaigns", current: false, component: markRaw(DripEmailMarketingCampaignsTab)},
                { name: "Marketing Consumers", current: false, component: markRaw(MarketingCampaignConsumersTable)},
                { name: "Logs", current: false, component: markRaw(MarketingLogsTable)},
                { name: "Management", current: false, component: markRaw(ManagementTab)},
                { name: "SMS Management", current: false, component: markRaw(SmsManagement)}
            ],
        }
    }
}
</script>
