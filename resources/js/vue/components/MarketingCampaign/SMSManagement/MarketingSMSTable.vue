<template>
    <div>
        <AlertsContainer :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
        <simple-table
            title="Marketing Texts"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            :table-filters="availableFilters"
            v-model="filter"
            @search="handleSearch"
            @reset="handleReset"
            @update:modelValue="getSMSData"
            row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
        >
            <template #row.col.message="{item,value}">
                <div class="text-sm truncate">{{value}}</div>
            </template>
            <template #row.col.actions="{item,value}">
                <simple-icon
                    @click="optOutPhone(item)"
                    :dark-mode="darkMode"
                    :icon="simpleIcon.icons.PHONE_X_MARK"
                    tooltip="Opt Out Phone Number from Marketing SMS"
                    :color="simpleIcon.colors.BLUE"
                />
            </template>
        </simple-table>
    </div>
</template>
<script>
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import Badge from "../../Shared/components/Badge.vue";
import ApiService from "./services/api.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {useAlerts} from "../../../composables/useAlerts.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
const simpleIcon = useSimpleIcon();
const {alertType, alertText, alertActive, displayAlert} = useAlerts();
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
}

export default {
    name: "MarketingSMSTable",
    components: {AlertsContainer, SimpleIcon, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            alertType,
            alertText,
            alertActive,
            displayAlert,
            simpleIcon,
            loading: false,
            headers: [
                {title: 'From', field: 'from'},
                {title: 'Direction', field: 'direction'},
                {title: 'Message', field: 'message_body', cols: 2},
                {title: 'Sent At', field: 'created_at'},
                {title: 'Actions', field: 'actions'},
            ],
            data: [],
            availableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'phone',
                    title: "Enter Phone Number"
                },
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'message',
                    title: "Enter Message"
                },
            ],
            filter: {},
            paginationData: {},
            selectedSMS: null,
            DEFAULT_TABLE_FILTER,
            api: ApiService.make(),
        }
    },
    emits: ['opt-out-requested'],
    created() {
        this.filter = {...this.DEFAULT_TABLE_FILTER}
        this.getSMSData();
    },
    methods: {
        async getSMSData() {
            this.loading = true;
            const response = await this.api.listMarketingSMS(this.filter);
            const { data, links, meta } = response.data
            this.data = data
            this.paginationData = { links, ...meta}
            this.loading = false;
        },
        async handleSearch() {
            this.filter = {...this.filter, ...DEFAULT_TABLE_FILTER};
            await this.getSMSData();
        },
        async handleReset() {
            this.filter = {...DEFAULT_TABLE_FILTER};
            await this.getSMSData();
        },
        async optOutPhone(item) {
            this.api.requestOptOut(item.from).then(resp => {
                this.displayAlert('Phone added to opt out list', 'success')
                this.$emit('opt-out-requested');
            }).catch(error => this.displayAlert('Opt Out Failed: ' + error.response.data.message, 'error'));
        }
    }
}
</script>
