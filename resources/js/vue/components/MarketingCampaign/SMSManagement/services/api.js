import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'marketing-campaigns', 1);
    }

    listMarketingSMS(params)
    {
        return this.axios().get('/sms', {params});
    }

    listOptedOutNumbers(params)
    {
        return this.axios().get('/sms/opt-out', {params})
    }

    requestOptOut(phone)
    {
        return this.axios().post('/sms/opt-out', {phone: phone})
    }

    unblockPhone(optOutId)
    {
        return this.axios().delete(`/sms/opt-out/${optOutId}`)
    }

}
