<template>
    <div>
        <AlertsContainer :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
        <simple-table
            title="SMS Opt-Out Numbers"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            :table-filters="availableFilters"
            v-model="filter"
            @search="handleSearch"
            @reset="handleReset"
            @update:modelValue="getOptOutNumbers"
            row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
        >
            <template #visible-filters>
                <custom-input
                    class="col-span-2"
                    v-model="filter.phone"
                    :dark-mode="darkMode"
                    placeholder="Enter Phone"
                />
            </template>
            <template #custom-buttons>
                <custom-button @click="showOptOutModal = !showOptOutModal" :dark-mode="darkMode">+ Phone</custom-button>
            </template>
            <template #row.col.reason="{value}">
                <div class="text-sm truncate">{{value || 'N/A'}}</div>
            </template>
            <template #row.col.actions="{item}">
                <simple-icon
                    @click="optInPhone(item)"
                    :dark-mode="darkMode"
                    :icon="simpleIcon.icons.X_MARK"
                    tooltip="Remove Phone from opt out list"
                    :color="simpleIcon.colors.BLUE"
                />
            </template>
        </simple-table>
        <modal
            v-if="showOptOutModal"
            @close="showOptOutModal = false"
            :dark-mode="darkMode"
            :disable-confirm="!validNumber"
            no-close-button
            @confirm="addOptOutNumber"
            custom-width="max-w-screen-sm"
        >
            <template #header>
                Add Opt Out Number
            </template>
            <template #content>
                <labeled-value label="Phone Number">
                    <custom-input v-model="optOutNumber" placeholder="Enter Phone Number" :dark-mode="darkMode" />
                </labeled-value>
                <transition-group
                    name="fade"
                    tag="div"
                    class="mt-4"
                >
                    <simple-alert
                        v-for="(error, key) in phoneValidationErrors"
                        :key="key"
                        :dark-mode="darkMode"
                        :content="error"
                        class="transition-all duration-300 ease-in-out mb-0"
                    />
                </transition-group>
            </template>
        </modal>
    </div>
</template>
<script>
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import ApiService from "./services/api.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {useAlerts} from "../../../composables/useAlerts.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Modal from "../../Shared/components/Modal.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
const simpleIcon = useSimpleIcon();
const {alertType, alertText, alertActive, displayAlert} = useAlerts();
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
}

export default {
    name: "OptOutNumbersTable",
    components: {SimpleAlert, LabeledValue, Modal, CustomButton, CustomInput, AlertsContainer, SimpleIcon, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            alertType,
            alertText,
            alertActive,
            displayAlert,
            simpleIcon,
            loading: false,
            headers: [
                {title: 'Phone Number', field: 'phone'},
                {title: 'Reason', field: 'reason'},
                {title: 'Opted Out At', field: 'created_at'},
                {title: 'Actions', field: 'actions'},
            ],
            data: [],
            availableFilters: [],
            filter: {},
            paginationData: {},
            selectedOptOut: null,
            DEFAULT_TABLE_FILTER,
            api: ApiService.make(),
            showOptOutModal: false,
            optOutNumber: "",
        }
    },
    created() {
        this.filter = {...this.DEFAULT_TABLE_FILTER}
        this.getOptOutNumbers();
    },
    computed: {
        phoneValidationErrors() {
            const errors = [];
            if (!this.optOutNumber) {
                errors.push('Phone number is required')
            }

            const value = this.optOutNumber.trim()
            const cleaned = value.replace(/\D/g, '')
            // Criteria 1: Minimum length
            if (cleaned.length < 10) {
                errors.push('Phone number must have at least 10 digits')
            }

            // Criteria 2: Maximum length
            if (cleaned.length > 15) {
                errors.push('Phone number cannot exceed 15 digits')
            }

            // Criteria 3: Valid format pattern
            const intlPhonePattern = /^(\+\d{1,3})?[\s.-]?\(?\d{1,4}\)?[\s.-]?\d{1,4}[\s.-]?\d{1,4}[\s.-]?\d{0,9}$/
            if (!intlPhonePattern.test(value)) {
                errors.push('Phone number format is invalid')
            }

            // Criteria 4: No invalid characters (optional - only allow digits, spaces, dashes, parentheses, plus)
            const validCharsPattern = /^[\d\s\-()+.]+$/
            if (!validCharsPattern.test(value)) {
                errors.push('Phone number contains invalid characters')
            }

            // Criteria 5: US-specific validation (if you want to enforce US format)
            if (cleaned.length === 10 || (cleaned.length === 11 && cleaned.startsWith('1'))) {
                const usPattern = /^(\+?1[-.\s]?)?\(?([2-9]\d{2})\)?[-.\s]?([2-9]\d{2})[-.\s]?(\d{4})$/
                if (!usPattern.test(value)) {
                    errors.push('US phone number format is invalid (area code and exchange cannot start with 0 or 1)')
                }
            }

            return errors
        },
        validNumber() {
            return this.phoneValidationErrors.length === 0;
        }
    },
    methods: {
        async addOptOutNumber() {
            await this.api.requestOptOut(this.optOutNumber)
                .then(() => {
                    this.displayAlert('Phone added to opt out list', 'success')
                    this.getOptOutNumbers()
                    this.showOptOutModal = false
                })
                .catch(() => this.displayAlert('Failed to add phone to opt out list', 'error'));
        },
        async optInPhone(item) {
            this.api.unblockPhone(item.id)
                .then(() => this.getOptOutNumbers())
                .catch(() => this.displayAlert('Failed to remove phone from opt out list', 'error'));
        },
        async getOptOutNumbers() {
            this.loading = true;
            const response = await this.api.listOptedOutNumbers(this.filter);
            const { data, links, meta } = response.data
            this.data = data
            this.paginationData = { links, ...meta}
            this.loading = false;
        },
        async handleSearch() {
            this.filter = {...this.filter, ...DEFAULT_TABLE_FILTER};
            await this.getOptOutNumbers();
        },
        async handleReset() {
            this.filter = {...DEFAULT_TABLE_FILTER};
            await this.getOptOutNumbers();
        },
    }
}
</script>
<style scoped>
/* Option 1: Simple fade */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
    opacity: 0;
}
</style>
