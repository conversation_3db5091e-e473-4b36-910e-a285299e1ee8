<template>
    <div class="grid grid-cols-5 gap-4">
        <MarketingSMSTable
            class="col-span-3"
            :dark-mode="darkMode"
            @opt-out-requested="handleOptOutRequested"
        />
        <OptOutNumbersTable
            class="col-span-2"
            ref="optOutTable"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>

import MarketingSMSTable from "./SMSManagement/MarketingSMSTable.vue";
import OptOutNumbersTable from "./SMSManagement/OptOutNumbersTable.vue";
export default {
    name: "SMSManagement",
    components: {OptOutNumbersTable, MarketingSMSTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    methods: {
        handleOptOutRequested() {
            this.$refs.optOutTable.handleSearch();
        }
    }
}
</script>
