<template>
    <div class="row-span-3 border rounded-lg mb-4 pb-5"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode, 'fixed top-[4rem] left-0 right-0 h-full z-10': expanded}">
        <div class="p-5">
            <div class="flex justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Assignments</h5>
                <div class="cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6" v-if="!expanded"
                         @click="expanded=true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6" v-if="expanded"
                         @click="expanded=false">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 9V4.5M9 9H4.5M9 9 3.75 3.75M9 15v4.5M9 15H4.5M9 15l-5.25 5.25M15 9h4.5M15 9V4.5M15 9l5.25-5.25M15 15h4.5M15 15v4.5m0-4.5 5.25 5.25" />
                    </svg>
                </div>
            </div>
            <alerts-container :dark-mode="darkMode" v-if="errors" :text="errors" :alert-type="'error'"></alerts-container>
            <loading-spinner v-if="loading"></loading-spinner>
            <div v-else>
                <tab
                    :tabs-classes="'w-full'"
                    :tab-type="'Normal'"
                    :default-tab-index="0"
                    :dark-mode="darkMode"
                    :tabs="tabs"
                    @selected="switchTab"
                >
                </tab>
                <div v-if="selectedTab === 'Assigned'" class="pt-7">
                    <div class="font-semibold text-md text-green-500 pb-5 mb-3" v-if="consumerProduct.sold_from_aged_queue">
                        Allocated from Aged Queue
                    </div>
                    <simple-table-header :grid-cols="7" :headers="tableHeader"
                                         :header-classes="'uppercase text-xs font-semibold text-center rounded-lg flex items-center'"/>
                    <simple-table-body :grid-cols="7" :data="assignments" :dark-mode="darkMode" :not-found-message="noDataMessage" :wrapper-classes="'min-h-80'"
                                       :not-found-wrapper-classes="'h-48'">
                        <div class="grid grid-cols-7 gap-x-5 border-b px-5 py-3"
                             :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                             v-for="assignment in assignments" :key="assignment.id">
                            <p class="text-sm">
                                <a :href="`/companies/${assignment.company_id}`" class="text-primary-500" target="_blank">{{ assignment.company_name }}</a>
                            </p>
                            <div class="text-sm flex items-center gap-2">
                                <p>{{ assignment.campaign_name }}</p>
                                <Tooltip v-if="assignment.has_campaign_filter" :dark-mode="darkMode">
                                    <template v-slot:icon>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                                        </svg>
                                    </template>
                                    <template v-slot:default>
                                        This campaign has active filters
                                    </template>
                                </Tooltip>
                            </div>
                            <p class="text-sm">
                                <Badge :color="productBadgeColor(assignment.product)">{{ assignment.product }}</Badge>
                            </p>
                            <div class="text-sm">
                                <p v-if="!editingSaleType || assignment.id !== selectedProductAssignment"
                                   :class="{'cursor-pointer text-primary-500': !assignment.invoiced && permissionStore.hasPermission(PERMISSIONS.PERMISSION_LEAD_ALLOCATION_AND_ADJUSTMENT)}"
                                   @click="editSaleType(assignment.sale_type, assignment)">
                                    {{ assignment.sale_type }}
                                </p>
                                <div v-if="editingSaleType && assignment.id === selectedProductAssignment">
                                    <dropdown :options="availableSaleTypes" v-model="selectedSaleType" :dark-mode="darkMode"></dropdown>
                                    <div class="flex gap-2 mt-3 flex-wrap">
                                        <custom-button @click="saveSaleType(assignment.id)" :disabled="savingSaleType" :height="'h-7'" :dark-mode="darkMode">Save</custom-button>
                                        <custom-button :color="'slate-outline'" @click="editingSaleType = false" :disabled="savingSaleType" :height="'h-7'" :dark-mode="darkMode">
                                            Cancel
                                        </custom-button>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs">
                                <div class="px-3 inline-flex items-center rounded-full py-1 whitespace-no-wrap"
                                     :class="[{'text-blue-550' : assignment.chargeable, 'text-red-350' : !assignment.chargeable}, {'bg-cyan-150': assignment.chargeable && !darkMode}, {'bg-red-100': !assignment.chargeable && !darkMode}]">
                                    <svg v-if="assignment.chargeable" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="9" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p class="font-semibold">{{ assignment.chargeable ? 'Yes' : 'No' }}</p>
                                </div>
                            </p>
                            <p class="text-xs">
                                <div class="px-3 inline-flex items-center rounded-full py-1 whitespace-no-wrap"
                                     :class="[{'text-blue-550' : assignment.delivered, 'text-red-350' : !assignment.delivered}, {'bg-cyan-150': assignment.delivered && !darkMode}, {'bg-red-100': !assignment.delivered && !darkMode}]">
                                    <svg v-if="assignment.delivered" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p class="font-semibold">{{ assignment.delivered ? 'Yes' : 'No' }}</p>
                                </div>
                            </p>
                            <p class="text-sm">${{ assignment.cost }}</p>
                        </div>
                    </simple-table-body>
                </div>
                <div v-if="selectedTab === 'Available'" class="pt-5">
                    <div v-if="reservedBy" class="mb-3">
                        <alert :alert-type="'error'" :dark-mode="darkMode" :text="`Lead is reserved by ${reservedBy.name}`"
                               :container-classes="'pt-5 w-full'"></alert>
                    </div>
                    <div v-if="allocationIsScheduled" class="mb-3">
                        <alert :alert-type="'error'" :dark-mode="darkMode" :text="'An allocation is scheduled'"
                               :container-classes="'pt-5 w-full'"></alert>
                    </div>
                    <ConsumerProductAllocation :dark-mode="darkMode" :consumer-product-id="consumerProductId" :selected-campaigns="selectedCampaigns" :assigned="assignments.length"
                                               :disable="allocating" @campaign-removed="removeSelectedCampaign" @allocate="allocateSelected"
                                               @processing="status => processing = status" @error="error => errors = error"/>
                    <div v-if="proposedAssignments.length">
                        <div class="font-semibold text-xl text-green-500 py-3 border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                            Available Campaigns (BRS)
                        </div>
                        <simple-table-header :grid-cols="6" :headers="availableBRSTableHeader"
                                             :header-classes="'uppercase text-xs font-semibold text-center rounded-lg flex items-center mt-3'"/>
                        <simple-table-body :grid-cols="6" :data="proposedAssignments" :dark-mode="darkMode" :not-found-message="noDataMessage" :wrapper-classes="'h-48'"
                                           :not-found-wrapper-classes="'h-48'">
                            <div class="grid grid-cols-6 gap-x-5 border-b px-5 py-3"
                                 :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                                 v-for="assignment in proposedAssignments" :key="assignment.company_id">
                                <p class="text-sm">
                                    <a :href="`/companies/${assignment.company_id}`" class="text-primary-500" target="_blank" >{{ assignment.company_name }}</a>
                                </p>
                                <p class="text-sm ">{{ assignment.campaign_name }}</p>
                                <p class="inline-flex flex-wrap items-center gap-2">
                                    <Badge :color="productBadgeColor(assignment.product)">{{ assignment.product }}</Badge>
                                </p>
                                <p class="text-sm">{{ assignment.sale_type }}</p>
                                <p class="text-sm">${{ assignment.cost }}</p>
                                <p class="text-sm">
                                    <custom-checkbox v-model="assignment.selected" :input-disabled="disableSelection" v-if="!hideSelection"></custom-checkbox>
                                </p>
                            </div>
                        </simple-table-body>
                    </div>
                    <div v-if="available.length">
                        <div class="font-semibold text-xl text-blue-550 py-3 border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                            Available Campaigns
                        </div>
                        <simple-table-header :grid-cols="6" :headers="availableCampaignTableHeader"
                                             :header-classes="'uppercase text-xs font-semibold text-center rounded-lg flex items-center mt-3'"/>
                        <simple-table-body :grid-cols="6" :data="available" :dark-mode="darkMode" :not-found-message="noDataMessage" :wrapper-classes="'h-48'"
                                           :not-found-wrapper-classes="'h-48'">
                            <div class="grid grid-cols-6 gap-x-5 border-b px-5 py-3"
                                 :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                                 v-for="campaign in available" :key="campaign.campaign_id">
                                <p class="text-sm">
                                    <a :href="`/companies/${campaign.company_id}`" class="text-primary-500" target="_blank" >{{ campaign.company_name }}</a>
                                </p>
                                <div class="text-sm flex items-center gap-2">
                                    <p>{{ campaign.campaign_name }}</p>
                                    <Tooltip v-if="campaign.has_active_filters" :dark-mode="darkMode">
                                        <template v-slot:icon>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                                            </svg>
                                        </template>
                                        <template v-slot:default>
                                            This campaign has active filters
                                        </template>
                                    </Tooltip>
                                </div>
                                <p class="inline-flex flex-wrap items-center gap-2">
                                    <Badge :color="productBadgeColor(campaign.product)">{{ campaign.product }}</Badge>
                                </p>
                                <p class="text-sm">{{ campaign.verified_budget_usage }}%</p>
                                <p class="text-sm">
                                    <span class="font-normal text-sm">
                                            <span-hover-tooltip>
                                                <template #title>
                                                    {{ formatRejectionPercentage(campaign.rejection_statistics?.manual_rejection_percentage) }}
                                                </template>
                                                Manual
                                            </span-hover-tooltip> |
                                            <span-hover-tooltip>
                                                <template #title>
                                                    {{ formatRejectionPercentage(campaign.rejection_statistics?.crm_rejection_percentage) }}
                                                </template>
                                                CRM
                                            </span-hover-tooltip> |
                                            <span-hover-tooltip>
                                                <template #title>
                                                    {{ formatRejectionPercentage(campaign.rejection_statistics?.overall_rejection_percentage) }}
                                                </template>
                                                Overall
                                            </span-hover-tooltip>
                                    </span>
                                </p>
                                <p class="text-sm ml-3">
                                    <custom-checkbox v-model="campaign.selected" :input-disabled="disableSelection" v-if="!hideSelection"></custom-checkbox>
                                </p>
                            </div>
                        </simple-table-body>
                    </div>
                    <div class="border-t border-b overflow-y-auto divide-y h-48"
                         v-if="!proposedAssignments.length && !available.length"
                         :class="[darkMode ? 'bg-dark-background  border-dark-border' : 'bg-light-background  border-light-border']">
                        <p class="w-full h-full flex justify-center items-center text-grey-400">No campaign to assign</p>
                    </div>
                </div>
                <div v-if="selectedTab === 'Allocation Job'" class="pt-5">
                    <div class="h-48 text-sm">
                        <div v-if="!jobStatuses.length">
                            <alert :alert-type="'warning'" :text="'No allocation scheduled'" :dark-mode="darkMode" :container-classes="'pt-5 w-full'" />
                        </div>
                        <div v-else v-for="jobStatus in jobStatuses">
                            <alert :alert-type="getAlertType(jobStatus)" :text="getStatusMessage(jobStatus)" :dark-mode="darkMode"
                                   :container-classes="'pt-5 w-full'"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>

import ConsumerApiService from "../../services/consumer_api";
import {computed, onMounted, ref} from "vue";
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import Tab from "../../components/Tab.vue";
import SimpleTableHeader from "../../components/SimpleTable/components/SimpleTableHeader.vue";
import SimpleTableBody from "../../components/SimpleTable/components/SimpleTableBody.vue";
import Alert from "../../components/Alert.vue";
import Badge from "../../components/Badge.vue";
import SpanHoverTooltip from "../../components/SpanHoverTooltip.vue";
import CustomCheckbox from "../../SlideWizard/components/CustomCheckbox.vue";
import ConsumerProductAllocation from "./CosumerProductAllocation.vue";
import AlertsContainer from "../../components/AlertsContainer.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import Dropdown from "../../components/Dropdown.vue";
import CustomButton from "../../components/CustomButton.vue";
import Tooltip from "../../components/Tooltip.vue";

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    },
    consumerProductId: {
        type: Number,
        required: true
    },
    disableAllocation: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['disable-allocation']);

const api = ConsumerApiService.make();
const permissionStore = useRolesPermissions();
const assignments = ref([]);
const proposedAssignments = ref([]);
const available = ref([]);
const jobStatuses = ref([]);
const consumerProduct = ref({});
const loading = ref(false);
const tabs = ref([]);
const selectedTab = ref('');
const tableHeader = [
    {title: 'Company', show: true},
    {title: 'Campaign', show: true},
    {title: 'Product', show: true},
    {title: 'Sale Type', show: true},
    {title: 'Chargeable', show: true},
    {title: 'Delivered', show: true},
    {title: 'Cost', show: true}
];
const availableBRSTableHeader = [
    {title: 'Company', show: true},
    {title: 'Campaign', show: true},
    {title: 'Product', show: true},
    {title: 'Sale Type', show: true},
    {title: 'Cost', show: true},
    {title: 'Select', show: true},
];
const availableCampaignTableHeader = [
    {title: 'Company', show: true},
    {title: 'Campaign', show: true},
    {title: 'Product', show: true},
    {title: 'Budget Usage', show: true},
    {title: 'Rejections', show: true},
    {title: 'Select', show: true},
]
const salesTypes = [
    'Exclusive',
    'Duo',
    'Trio',
    'Quad'
];
const selectedSaleType = ref(null);
const selectedProductAssignment = ref(null);
const processing = ref(false);
const allocating = ref(false);
const expanded = ref(false);
const reservedBy = ref(null);
const errors = ref(null);
const editingSaleType = ref(false);
const savingSaleType = ref(false);

onMounted(() => initialize());

const initialize = async () => {
    loading.value = true;
    initializeTabs();

    await api.getAssignments(props.consumerProductId).then(resp => {
        if (resp.data?.data?.status) {
            assignments.value = resp.data.data.assignments?.assigned ?? [];
            proposedAssignments.value = resp.data.data.assignments?.proposed ?? [];
            available.value = resp.data.data.assignments?.available ?? [];
            jobStatuses.value = resp.data.data.allocation_job_status ?? [];
            reservedBy.value = resp.data.data.assignments?.reserved_by ?? null
            consumerProduct.value = resp.data.data.consumer_product ?? {};
        }
    });

    proposedAssignments.value.forEach(campaign => campaign.selected = false);
    available.value.forEach(campaign => campaign.selected = false);

    if (allocationScheduled()) {
        emit('disable-allocation', 'An allocation is already scheduled');
    }

    loading.value = false;
}
const initializeTabs = () => {
    tabs.value = [
        {name: 'Assigned', current: true},
        {name: 'Available', current: false},
        {name: 'Allocation Job', current: false}
    ];
    selectedTab.value = 'Assigned';
};
const switchTab = tab => selectedTab.value = tab;
const allocationIsScheduled = computed(() => !!jobStatuses.value.find(jobStatus => jobStatus.status === 'Scheduled'));
const noDataMessage = computed(() => selectedTab.value === 'Assigned' ? 'No assignments' : 'No campaign to assign');
const disableSelection = computed(() => processing.value || allocating.value);
const hideSelection = computed(() =>
    selectedCampaigns.value.length + assignments.value.length >= consumerProduct.value.contact_requests
    || reservedBy.value
    || allocationIsScheduled.value
    || !permissionStore.hasPermission(PERMISSIONS.PERMISSION_LEAD_ALLOCATION_AND_ADJUSTMENT)
    || props.disableAllocation
);
const selectedCampaigns = computed(() => [
        ...proposedAssignments.value.filter(campaign => campaign.selected),
        ...available.value.filter(campaign => campaign.selected)
    ]);
const availableSaleTypes = computed(() => {
    switch (consumerProduct.value.contact_requests) {
        case 1:
            return [...salesTypes].splice(0, 1);
        case 2:
            return [...salesTypes].splice(0, 2);
        case 3:
            return [...salesTypes].splice(0, 3);
        case 4:
            return [...salesTypes].splice(0, 4);
        default:
            return [...salesTypes];
    }
});
const getAlertType = (jobStatus) => {
    switch (jobStatus.status) {
        case 'Completed':
            return 'success';
        case 'Failed':
            return 'error';
        case 'Scheduled':
            return 'info';
        default:
            return '';
    }
};
const getStatusMessage = (jobStatus) => {
    if (!jobStatus.status_timestamp) {
        return '';
    }

    const dateTime = new Date(jobStatus.status_timestamp * 1000).toLocaleString('en', {timeZone: 'America/Denver'});

    switch (jobStatus.status) {
        case 'Completed':
            return `Allocation job was completed at ${dateTime} MST. Attempts: ${jobStatus.attempts}`;
        case 'Failed':
            return `Allocation job was failed at ${dateTime} MST. Attempts: ${jobStatus.attempts}`;
        case 'Scheduled':
            return `Allocation job is scheduled at ${dateTime} MST. Attempts: ${jobStatus.attempts}`;
        default:
            return '';
    }
}

const allocationScheduled = () => {
    if (!jobStatuses.value.length) {
        return false;
    }

    return jobStatuses.value[0]?.status === 'Scheduled';
}

const productBadgeColor = product => {
    switch (product.toLowerCase()) {
        case 'lead':
            return 'cyan';
        case 'direct leads':
            return 'green';
        default:
            return 'indigo';
    }
};

const formatRejectionPercentage = percentage => {
    if (percentage !== undefined) return `${percentage}%`;
    return 'N/A';
};

const removeSelectedCampaign = campaignId => {
    const campaign = selectedCampaigns.value.find(campaign => campaign.campaign_id === campaignId && campaign.selected);

    if (campaign) {
        campaign.selected = false;
    }
};

const allocateSelected = saleType => {
    allocating.value = true;
    errors.value = null;

    api.allocate(props.consumerProductId, {
        campaign_ids: selectedCampaigns.value.map(campaign => campaign.campaign_id),
        sale_type: saleType
    }).then((resp) => {
        if (!resp.data.data.status) {
            errors.value = resp.data.data.error ?? 'Something went wrong';
        } else {
            initialize();
        }
    }).catch(e => {
        errors.value ='Something went wrong';
        console.error(e);
    }).finally(() => allocating.value = false);
};

const editSaleType = (saleType, assignment) =>  {
    if (!permissionStore.hasPermission(PERMISSIONS.PERMISSION_LEAD_ALLOCATION_AND_ADJUSTMENT) || assignment.invoiced) {
        return;
    }

    selectedSaleType.value = saleType;
    editingSaleType.value = true;
    selectedProductAssignment.value = assignment.id;
}

const saveSaleType = id => {
    savingSaleType.value = true;
    errors.value = null;

    api.saveSaleType(props.consumerProductId, id, selectedSaleType.value)
        .then(resp => {
            if (!resp.data.data.status) {
                errors.value = resp.data.data.error ?? 'Something went wrong';
            } else {
                editingSaleType.value = false;
                initialize();
            }
        })
        .catch(e => {
            errors.value = 'Something went wrong';
            console.error(e);
        })
        .finally(() => savingSaleType.value = false) ;
};

</script>
