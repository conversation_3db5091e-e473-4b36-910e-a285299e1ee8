<?php

namespace Feature\Marketing;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Services\EmailAddress\EmailAddressService;
use App\Services\MarketingCampaign\Events\ClickedEvent;
use App\Services\MarketingCampaign\Events\ComplaintEvent;
use App\Services\MarketingCampaign\Events\DeliveredEvent;
use App\Services\MarketingCampaign\Events\FailedEvent;
use App\Services\MarketingCampaign\Events\OpenedEvent;
use App\Services\MarketingCampaign\Events\SMSOptOutEvent;
use App\Services\MarketingCampaign\Events\SMSResponseEvent;
use App\Services\MarketingCampaign\SocketLabsEventInterpreter;
use App\Services\MarketingCampaign\TwilioEventInterpreter;
use App\Services\PhoneNumber\PhoneNumberService;
use App\Services\SMSOptOut\SmsOptOutService;
use App\Exceptions\CustomValidationException;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class SMSOptOutTest extends TestCase
{
    use RefreshDatabase;

    private PhoneNumberService $phoneNumberService;
    private SmsOptOutService $smsOptOutService;

    public function setUp(): void
    {

        $this->phoneNumberService = app(PhoneNumberService::class);

        $this->smsOptOutService = app(SmsOptOutService::class);

        parent::setUp(); // TODO: Change the autogenerated stub
    }


    #[DataProvider('phoneNumberNormalizeProvider')]
    public function test_normalise_phone_number(string $input, ?string $expected)
    {
        $result = $this->phoneNumberService->normalisePhoneNumber($input);
        $this->assertEquals($expected, $result);
    }

    #[DataProvider('phoneNumberCheckProvider')]
    public function test_sms_opt_out_check(string $phone, bool $shouldBeOptedOut)
    {
        if ($shouldBeOptedOut) {
            try {
                $this->smsOptOutService->add($phone, 'Test opt out');
            } catch (CustomValidationException) {
                // Phone number already exists, which is fine for testing
            }
        }

        $result = $this->smsOptOutService->check($phone);
        $this->assertEquals($shouldBeOptedOut, $result);
    }

    public static function phoneNumberNormalizeProvider(): array
    {
        return [
            // Valid 10-digit numbers
            ['**********', '+1**********'],
            ['************', '+19154073196'],
            ['************', '+13187299033'],
            ['**********', '+1**********'],
            ['************', '+19016924559'],
            ['**********', '+1**********'],
            ['**********', '+1**********'],
            ['************', '+15205556414'],
            ['**********', '+1**********'],
            ['************', '+14782329122'],
            ['************', '+12819292667'],

            // Valid 11-digit numbers starting with 1
            ['1**********', '+1**********'],
            ['1-************', '+19154073196'],
            ['**************', '+13187299033'],
            ['**************', '+1**********'],

            // Invalid numbers
            ['**********', '+1**********'], // Valid format but invalid number
            ['123', null], // Too short
            ['12345678901234', null], // Too long
            ['22123456789', null], // 11 digits not starting with 1
            ['abc123def456', null], // Contains letters
            ['', null], // Empty string
            [' ', null], // Just whitespace
        ];
    }

    public static function phoneNumberCheckProvider(): array
    {
        return [
            // Numbers that should be opted out
            ['**********', true],
            ['************', true],
            ['************', true],
            ['**********', true],
            ['************', true],
            ['**********', true],

            // Numbers that should not be opted out
            ['**********', false],
            ['************', false],
            ['**********', false],
            ['************', false],
            ['************', false],
        ];
    }

}
