<?php

namespace Feature\PhoneNumber;

use App\Enums\Odin\StateAbbreviation;
use App\Services\PhoneNumber\PhoneNumberService;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class PhoneNumberServiceTest extends TestCase
{
    private PhoneNumberService $service;

    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub
        $this->service = app(PhoneNumberService::class);
    }

    public static function validSendingTimeProvider(): array
    {
        return [
            // Valid state abbreviations - 8:00-21:00 in their timezone
            'NY valid morning' => ['NY', '08:00:00', true],
            'NY valid afternoon' => ['NY', '15:30:00', true],
            'NY valid evening' => ['NY', '20:54:59', true],
            'NY invalid early morning' => ['NY', '07:59:59', false],
            'NY invalid late night' => ['NY', '21:00:00', false],

            'CA valid morning' => ['CA', '08:00:00', true],
            'CA valid afternoon' => ['CA', '14:30:00', true],
            'CA valid evening' => ['CA', '20:54:59', true],
            'CA invalid early morning' => ['CA', '07:59:59', false],
            'CA invalid late night' => ['CA', '21:00:00', false],

            'TX valid morning' => ['TX', '08:00:00', true],
            'TX valid afternoon' => ['TX', '16:45:00', true],
            'TX valid evening' => ['TX', '20:54:59', true],
            'TX invalid early morning' => ['TX', '07:59:59', false],
            'TX invalid late night' => ['TX', '21:00:00', false],

            'CO valid morning' => ['CO', '08:00:00', true],
            'CO valid afternoon' => ['CO', '12:00:00', true],
            'CO valid evening' => ['CO', '20:54:59', true],
            'CO invalid early morning' => ['CO', '07:59:59', false],
            'CO invalid late night' => ['CO', '21:00:00', false],

            'AK valid morning' => ['AK', '08:00:00', true],
            'AK valid afternoon' => ['AK', '13:15:00', true],
            'AK valid evening' => ['AK', '20:54:59', true],
            'AK invalid early morning' => ['AK', '07:59:59', false],
            'AK invalid late night' => ['AK', '21:00:00', false],

            'HI valid morning' => ['HI', '08:00:00', true],
            'HI valid afternoon' => ['HI', '17:30:00', true],
            'HI valid evening' => ['HI', '20:54:59', true],
            'HI invalid early morning' => ['HI', '07:59:59', false],
            'HI invalid late night' => ['HI', '21:00:00', false],

            // Different timezone examples
            'FL valid morning' => ['FL', '08:00:00', true],
            'FL valid evening' => ['FL', '20:54:59', true],
            'FL invalid early' => ['FL', '07:59:59', false],
            'FL invalid late' => ['FL', '21:00:00', false],

            'OR valid morning' => ['OR', '08:00:00', true],
            'OR valid evening' => ['OR', '20:54:59', true],
            'OR invalid early' => ['OR', '07:59:59', false],
            'OR invalid late' => ['OR', '21:00:00', false],

            // Edge cases - exactly at boundaries
            'NY exactly at start' => ['NY', '08:00:00', true],
            'NY exactly at end minus 1 second' => ['NY', '20:54:59', true],
            'NY exactly at end' => ['NY', '21:00:00', false],

            // Invalid/null state - defaults to NY timezone with 11:00-21:00
            'null state valid morning' => [null, '11:00:00', true],
            'null state valid afternoon' => [null, '15:30:00', true],
            'null state valid evening' => [null, '20:54:59', true],
            'null state invalid early morning' => [null, '10:59:59', false],
            'null state invalid late night' => [null, '21:00:00', false],

            'invalid state valid morning' => ['XX', '11:00:00', true],
            'invalid state valid afternoon' => ['XX', '18:45:00', true],
            'invalid state valid evening' => ['XX', '20:54:59', true],
            'invalid state invalid early' => ['XX', '10:59:59', false],
            'invalid state invalid late' => ['XX', '21:00:00', false],

            'empty string state valid' => ['', '15:00:00', true],
            'empty string state invalid early' => ['', '10:59:59', false],
            'empty string state invalid late' => ['', '21:00:00', false],

            // Additional edge cases for different timezones
            'UT valid mountain time' => ['UT', '08:00:00', true],
            'UT valid mountain time evening' => ['UT', '20:54:59', true],
            'UT invalid mountain time early' => ['UT', '07:59:59', false],
            'UT invalid mountain time late' => ['UT', '21:00:00', false],

            // Test various times throughout the day
            'CA noon' => ['CA', '12:00:00', true],
            'CA 9 AM' => ['CA', '09:00:00', true],
            'CA 7 PM' => ['CA', '19:00:00', true],
            'CA 6 AM' => ['CA', '06:00:00', false],
            'CA 10 PM' => ['CA', '22:00:00', false],
            'CA midnight' => ['CA', '00:00:00', false],
        ];
    }

    #[DataProvider('validSendingTimeProvider')]
    public function testValidSendingTime(?string $stateAbbreviation, string $time, bool $expected): void
    {
        if ($stateAbbreviation && StateAbbreviation::tryFrom($stateAbbreviation)) {
            $timezone = StateAbbreviation::timeZone($stateAbbreviation);
        } else {
            $timezone = StateAbbreviation::timeZone(StateAbbreviation::NY->value);
        }

        Carbon::setTestNow(Carbon::parse($time, $timezone));

        $result = $this->service->validSendingTime($stateAbbreviation);

        $expectedWord = $expected ? 'true' : 'false';

        $this->assertEquals($expected, $result,
            "Expected validSendingTime to return $expectedWord for state '$stateAbbreviation' at time '$time' in timezone '$timezone'"
        );

        Carbon::setTestNow();
    }

}
